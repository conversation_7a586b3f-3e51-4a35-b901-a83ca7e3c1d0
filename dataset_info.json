{"dataset_name": "infant_gross_motor_datasets", "description": "婴幼儿粗大运动发展指导数据集 - 用于训练AI模型进行婴幼儿发育指导的综合数据集", "version": "1.0.0", "language": "zh-CN", "license": "Academic Research Only", "created_date": "2024-12-25", "total_datasets": 15, "total_entries": 4586, "main_datasets": {"final_gross_motor_dataset.jsonl": {"description": "主要综合数据集 - 推荐使用", "format": "JSONL", "entries": 614, "size_mb": 1.2, "sources": ["base_generation", "literature_extraction", "professional_knowledge"], "quality_score": 100, "recommended": true}, "final_gross_motor_alpaca.json": {"description": "主要数据集的Alpaca格式版本", "format": "Alpaca JSON", "entries": 614, "size_mb": 1.1, "recommended": true}, "final_gross_motor_chatml.jsonl": {"description": "主要数据集的ChatML格式版本", "format": "ChatML", "entries": 614, "size_mb": 1.3, "recommended": true}}, "enhanced_datasets": {"comprehensive_motor_dataset.jsonl": {"description": "最全面的数据集版本", "format": "JSONL", "entries": 758, "size_mb": 1.5, "sources": ["base_generation", "literature_extraction", "professional_knowledge", "guidance_enhanced"], "recommended_for": "large_scale_training"}, "guidance_enhanced_dataset.jsonl": {"description": "基于Excel指导文件的增强数据", "format": "JSONL", "entries": 104, "size_mb": 0.2, "sources": ["guidance_excel"], "features": ["precise_angles", "specific_timeframes", "detailed_descriptions"]}, "literature_based_dataset.jsonl": {"description": "从医学文献提取的专业知识", "format": "JSONL", "entries": 186, "size_mb": 0.4, "sources": ["medical_literature"], "literature_count": 4}, "llm_enhanced_dataset.jsonl": {"description": "LLM增强生成的专业问答", "format": "JSONL", "entries": 26, "size_mb": 0.1, "sources": ["qwen_api"], "features": ["professional_qa", "detailed_explanations"]}}, "usage_recommendations": {"general_training": "final_gross_motor_dataset.jsonl", "large_scale_training": "comprehensive_motor_dataset.jsonl", "llama_alpaca": "*_alpaca.json", "chatglm_qwen": "*_chatml.jsonl", "research_analysis": "literature_based_dataset.jsonl"}}