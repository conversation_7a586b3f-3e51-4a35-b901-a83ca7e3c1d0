"""
基于文献的数据生成器（简化版）
直接基于提取的专业知识生成训练数据，不依赖API调用
"""

import json
import re
import random
from typing import List, Dict, Tuple
from dataset_schema import DatasetSchema, MotorSkillCategory, AgeGroup, InteractionType

class LiteratureDataGenerator:
    """基于文献的数据生成器"""
    
    def __init__(self):
        self.schema = DatasetSchema()
        self.knowledge_base = {
            'reflexes': {},
            'milestones': {},
            'development_patterns': {},
            'assessment_methods': {}
        }
    
    def load_and_process_json_files(self) -> Dict:
        """加载并处理JSON文件"""
        json_files = {
            "江钟立版": "人体发育学粗大运动 第2版 (江钟立主编, 江钟立主编, 江钟立) (Z-Library)/tmp-convert-175318645594011.json",
            "李晓捷版": "人体发育学粗大运动 第2版 (李晓捷主编, 主编李晓捷, 李晓捷, 李晓捷主编, 李晓捷) (Z-Library)/tmp-convert-17531864008521732.json",
            "左天香版": "人体发育学粗大运动 (左天香，徐冬晨主编, 左天香, 徐冬晨主编, 左天香, 徐冬晨) (Z-Library)/tmp-convert-17531868070407812.json",
            "习题集": "《人体发育学学习指导及习题集》粗大运动 (Pdg2Pic, 陈翔主编；吕智海，李林，李晓捷等编) (Z-Library)-compressed/tmp-convert-17531873488369693.json"
        }
        
        all_texts = []
        for name, file_path in json_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    texts = self._extract_text_from_json(data)
                    all_texts.extend(texts)
                    print(f"✅ 处理 {name}: {len(texts)} 段文本")
            except Exception as e:
                print(f"❌ 处理 {name} 失败: {e}")
        
        return all_texts
    
    def _extract_text_from_json(self, json_data: Dict) -> List[str]:
        """从JSON数据中提取文本"""
        texts = []
        if 'pages' in json_data:
            for page in json_data['pages']:
                if 'content' in page:
                    page_text = []
                    for content in page['content']:
                        if 'text' in content and content['text'].strip():
                            page_text.append(content['text'].strip())
                    if page_text:
                        texts.append(' '.join(page_text))
        return texts
    
    def extract_professional_knowledge(self, texts: List[str]) -> None:
        """提取专业知识"""
        print("🔍 提取专业知识...")
        
        # 提取反射信息
        self._extract_reflexes(texts)
        print(f"✅ 提取反射: {len(self.knowledge_base['reflexes'])} 个")
        
        # 提取里程碑信息
        self._extract_milestones(texts)
        print(f"✅ 提取里程碑: {len(self.knowledge_base['milestones'])} 个年龄段")
        
        # 提取发育模式
        self._extract_development_patterns(texts)
        print(f"✅ 提取发育模式: {len(self.knowledge_base['development_patterns'])} 个")
        
        # 提取评估方法
        self._extract_assessment_methods(texts)
        print(f"✅ 提取评估方法: {len(self.knowledge_base['assessment_methods'])} 个")
    
    def _extract_reflexes(self, texts: List[str]) -> None:
        """提取反射信息"""
        reflex_patterns = [
            r'(\w*反射).*?(\d+[~～-]\d+个?月)',
            r'(\w*反射).*?存在时期.*?(\d+[~～-]\d+个?月)',
            r'(\w*反射).*?持续.*?(\d+[~～-]\d+个?月)'
        ]
        
        for text in texts:
            for pattern in reflex_patterns:
                matches = re.findall(pattern, text)
                for reflex_name, time_period in matches:
                    if len(reflex_name) > 2:  # 过滤太短的匹配
                        self.knowledge_base['reflexes'][reflex_name] = {
                            'name': reflex_name,
                            'time_period': time_period,
                            'context': self._get_context(text, reflex_name, 150)
                        }
    
    def _extract_milestones(self, texts: List[str]) -> None:
        """提取里程碑信息"""
        milestone_patterns = [
            r'(\d+)个月.*?(抬头|翻身|坐|爬|站|走|跳|独\w+|扶\w+)',
            r'(\d+)个月.*?(可以|能够|会).*?(\w{2,8})',
            r'(\d+)个月.*?发育.*?(\w{3,10})'
        ]
        
        for text in texts:
            for pattern in milestone_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) >= 2:
                        age = match[0]
                        skill = match[1] if len(match) == 2 else match[1] + match[2]
                        
                        if age not in self.knowledge_base['milestones']:
                            self.knowledge_base['milestones'][age] = []
                        
                        self.knowledge_base['milestones'][age].append({
                            'skill': skill,
                            'context': self._get_context(text, f"{age}个月", 100)
                        })
    
    def _extract_development_patterns(self, texts: List[str]) -> None:
        """提取发育模式"""
        pattern_keywords = ['发育特点', '发育规律', '发育顺序', '姿势运动', '运动模式']
        
        for i, text in enumerate(texts):
            for keyword in pattern_keywords:
                if keyword in text:
                    self.knowledge_base['development_patterns'][f"pattern_{i}_{keyword}"] = {
                        'keyword': keyword,
                        'content': text[:300]  # 取前300字符
                    }
    
    def _extract_assessment_methods(self, texts: List[str]) -> None:
        """提取评估方法"""
        assessment_keywords = ['检查方法', '评定', '测查', '观察', '反应']
        
        for i, text in enumerate(texts):
            for keyword in assessment_keywords:
                if keyword in text:
                    self.knowledge_base['assessment_methods'][f"method_{i}_{keyword}"] = {
                        'keyword': keyword,
                        'content': text[:200]
                    }
    
    def _get_context(self, text: str, keyword: str, length: int = 100) -> str:
        """获取关键词上下文"""
        index = text.find(keyword)
        if index == -1:
            return text[:length]
        
        start = max(0, index - length // 2)
        end = min(len(text), index + len(keyword) + length // 2)
        return text[start:end].strip()
    
    def generate_reflex_qa_pairs(self) -> List[Dict]:
        """生成反射相关问答对"""
        qa_pairs = []
        
        question_templates = [
            "我的宝宝{age}，{reflex_name}是什么？什么时候出现和消失？",
            "{reflex_name}异常会有什么表现？需要注意什么？",
            "如何检查宝宝的{reflex_name}是否正常？",
            "{reflex_name}持续存在会影响宝宝发育吗？",
            "宝宝{age}了，{reflex_name}还很明显，正常吗？"
        ]
        
        for reflex_name, reflex_info in self.knowledge_base['reflexes'].items():
            time_period = reflex_info['time_period']
            context = reflex_info['context']
            
            # 提取年龄信息
            age_match = re.search(r'(\d+)', time_period)
            age = f"{age_match.group(1)}个月" if age_match else "几个月"
            
            for template in question_templates[:3]:  # 每个反射生成3个问答对
                question = template.format(reflex_name=reflex_name, age=age)
                
                answer = f"关于{reflex_name}：\n\n"
                answer += f"• 存在时期：{time_period}\n"
                answer += f"• 临床意义：{reflex_name}是评估婴幼儿神经发育的重要指标\n"
                
                if "异常" in template:
                    answer += f"• 异常表现：缺如、减弱、亢进或延迟消失都可能提示神经发育问题\n"
                    answer += f"• 注意事项：如发现异常应及时咨询儿科医生进行专业评估\n"
                elif "检查" in template:
                    answer += f"• 检查要点：观察反射的出现时间、强度和消失时间\n"
                    answer += f"• 评估环境：在安静、温暖的环境中进行，避免宝宝饥饿或过度疲劳\n"
                elif "持续" in template:
                    answer += f"• 影响：{reflex_name}持续存在可能影响后续运动技能发育\n"
                    answer += f"• 建议：建议进行专业发育评估，制定个性化训练方案\n"
                else:
                    answer += f"• 发育意义：{reflex_name}是婴儿期重要的神经反射，为后续运动发育奠定基础\n"
                
                answer += f"\n⚠️ 注意：每个宝宝发育进度不同，如有疑虑请咨询专业医生。"
                
                qa_pairs.append({
                    'question': question,
                    'answer': answer,
                    'category': 'reflex',
                    'age_group': age
                })
        
        return qa_pairs
    
    def generate_milestone_qa_pairs(self) -> List[Dict]:
        """生成里程碑相关问答对"""
        qa_pairs = []
        
        question_templates = [
            "我的宝宝{age}了，应该掌握哪些粗大运动技能？",
            "{age}的宝宝{skill}发育正常吗？",
            "如何帮助{age}的宝宝练习{skill}？",
            "宝宝{age}还不会{skill}，需要担心吗？",
            "{age}宝宝{skill}发育的标准是什么？"
        ]
        
        for age, milestones in self.knowledge_base['milestones'].items():
            if not milestones:
                continue
                
            main_skills = [m['skill'] for m in milestones[:3]]
            skill_text = '、'.join(main_skills)
            
            for i, template in enumerate(question_templates):
                if i < len(main_skills):
                    skill = main_skills[i]
                else:
                    skill = random.choice(main_skills)
                
                question = template.format(age=f"{age}个月", skill=skill)
                
                answer = f"{age}个月宝宝粗大运动发育指导：\n\n"
                
                if "应该掌握" in template:
                    answer += f"• 主要技能：{skill_text}\n"
                    answer += f"• 发育特点：这个阶段宝宝运动能力快速发展\n"
                    answer += f"• 个体差异：发育时间可能前后相差1-2个月\n"
                elif "如何帮助" in template:
                    answer += f"• 练习方法：为宝宝提供安全的练习环境\n"
                    answer += f"• 引导技巧：使用玩具吸引，鼓励主动尝试\n"
                    answer += f"• 循序渐进：从简单动作开始，逐步增加难度\n"
                elif "需要担心" in template:
                    answer += f"• 正常范围：{age}个月前后1-2个月出现都属正常\n"
                    answer += f"• 观察要点：关注宝宝整体发育情况\n"
                    answer += f"• 就医指征：如明显落后同龄儿童建议咨询医生\n"
                else:
                    answer += f"• 发育标准：{skill}通常在{age}个月左右出现\n"
                    answer += f"• 评估要点：观察动作的协调性和稳定性\n"
                
                answer += f"• 促进发育：多与宝宝互动，提供丰富的运动体验\n"
                answer += f"\n💡 建议：每个宝宝都是独特的，发育进度会有个体差异，保持耐心和鼓励很重要。"
                
                qa_pairs.append({
                    'question': question,
                    'answer': answer,
                    'category': 'milestone',
                    'age_group': f"{age}个月"
                })
        
        return qa_pairs
    
    def generate_assessment_qa_pairs(self) -> List[Dict]:
        """生成评估相关问答对"""
        qa_pairs = []
        
        assessment_questions = [
            "如何评估婴幼儿的粗大运动发育情况？",
            "什么时候需要进行专业的发育评估？",
            "家长在家如何观察宝宝的运动发育？",
            "粗大运动发育迟缓有哪些表现？",
            "如何区分正常的个体差异和发育异常？"
        ]
        
        assessment_answers = [
            "婴幼儿粗大运动发育评估要点：\n\n• 里程碑检查：对照标准发育里程碑，评估各项技能出现时间\n• 动作质量：观察动作的协调性、流畅性和稳定性\n• 肌张力：评估肌肉张力是否正常\n• 反射检查：检查原始反射、立直反射和平衡反应\n• 姿势观察：评估各种姿势下的运动表现\n\n💡 建议：定期记录宝宝的发育进展，有助于及时发现问题。",
            
            "需要专业发育评估的情况：\n\n• 明显延迟：运动里程碑明显落后同龄儿童2个月以上\n• 异常模式：出现异常的运动模式或姿势\n• 肌张力异常：肌肉过紧或过松\n• 反射异常：原始反射延迟消失或立直反射缺如\n• 家长担忧：对宝宝发育有任何担忧\n\n⚠️ 提醒：早期发现和干预对改善预后非常重要。",
            
            "家庭观察要点：\n\n• 日常记录：记录宝宝新技能出现的时间\n• 对比观察：与同龄宝宝进行适当对比\n• 质量评估：关注动作的协调性和稳定性\n• 进步追踪：观察技能的持续改善\n• 异常识别：注意不对称或异常的运动模式\n\n📝 建议：建立发育记录本，定期记录观察结果。",
            
            "粗大运动发育迟缓的表现：\n\n• 里程碑延迟：各项运动技能出现时间明显落后\n• 肌张力异常：肌肉过紧、过松或不稳定\n• 协调性差：动作不协调、不流畅\n• 平衡困难：平衡能力明显不足\n• 疲劳易倦：运动耐力差，容易疲劳\n\n🔍 注意：需要专业评估确定是否为真正的发育迟缓。",
            
            "个体差异vs发育异常：\n\n• 正常差异：在正常范围内的时间差异（通常1-2个月）\n• 发育异常：明显超出正常范围或伴有其他异常\n• 观察要点：整体发育趋势比单一指标更重要\n• 专业判断：复杂情况需要专业医生评估\n• 动态观察：持续观察比单次评估更有意义\n\n💭 提醒：保持理性观察，避免过度焦虑。"
        ]
        
        for question, answer in zip(assessment_questions, assessment_answers):
            qa_pairs.append({
                'question': question,
                'answer': answer,
                'category': 'assessment',
                'age_group': '0-36个月'
            })
        
        return qa_pairs
    
    def generate_complete_dataset(self) -> None:
        """生成完整的基于文献的数据集"""
        print("🚀 开始生成基于文献的数据集...")
        
        # 1. 加载和处理JSON文件
        texts = self.load_and_process_json_files()
        print(f"📄 总计处理文本: {len(texts)} 段")
        
        # 2. 提取专业知识
        self.extract_professional_knowledge(texts)
        
        # 3. 生成各类问答对
        print("📝 生成问答对...")
        reflex_qa = self.generate_reflex_qa_pairs()
        milestone_qa = self.generate_milestone_qa_pairs()
        assessment_qa = self.generate_assessment_qa_pairs()
        
        all_qa = reflex_qa + milestone_qa + assessment_qa
        print(f"✅ 生成问答对: {len(all_qa)} 个")
        print(f"  - 反射相关: {len(reflex_qa)} 个")
        print(f"  - 里程碑相关: {len(milestone_qa)} 个")
        print(f"  - 评估相关: {len(assessment_qa)} 个")
        
        # 4. 转换为数据集格式
        dataset_entries = []
        for qa in all_qa:
            entry = self.schema.create_generic_entry(
                input_text=qa['question'],
                output_text=qa['answer'],
                interaction_type="guidance"
            )
            dataset_entries.append(entry)
        
        # 5. 保存数据集
        self._save_dataset(dataset_entries, all_qa)
        
        # 6. 保存知识库
        with open("literature_knowledge_base.json", 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
        print("📚 知识库已保存")
        
        print("🎉 基于文献的数据集生成完成！")
    
    def _save_dataset(self, entries: List, qa_data: List[Dict]) -> None:
        """保存数据集"""
        # JSONL格式
        with open("literature_based_dataset.jsonl", 'w', encoding='utf-8') as f:
            for qa in qa_data:
                jsonl_entry = {
                    "instruction": qa['question'],
                    "output": qa['answer'],
                    "category": qa['category'],
                    "age_group": qa['age_group'],
                    "source": "literature"
                }
                f.write(json.dumps(jsonl_entry, ensure_ascii=False) + '\n')
        
        # Alpaca格式
        alpaca_data = []
        for qa in qa_data:
            alpaca_data.append({
                "instruction": qa['question'],
                "input": "",
                "output": qa['answer']
            })
        
        with open("literature_based_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 数据集已保存:")
        print(f"  - JSONL格式: literature_based_dataset.jsonl")
        print(f"  - Alpaca格式: literature_based_alpaca.json")

if __name__ == "__main__":
    generator = LiteratureDataGenerator()
    generator.generate_complete_dataset()
