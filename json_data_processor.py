"""
JSON文献数据处理器
分析转换后的PDF JSON文件，提取专业知识构建数据集
"""

import json
import re
from typing import List, Dict, Tuple
from dataset_schema import DatasetSchema, MotorSkillCategory, AgeGroup, InteractionType
from qwen_api_client import QwenAPIClient, QwenConfig

class JSONDataProcessor:
    """JSON文献数据处理器"""
    
    def __init__(self):
        self.schema = DatasetSchema()
        self.extracted_knowledge = {
            'reflexes': {},
            'milestones': {},
            'development_stages': {},
            'assessment_methods': {},
            'abnormal_patterns': {}
        }
        
        # 配置Qwen API
        config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
        self.qwen_client = QwenAPIClient(config)
    
    def load_json_files(self) -> Dict[str, Dict]:
        """加载所有JSON文件"""
        json_files = {
            "江钟立版": "人体发育学粗大运动 第2版 (江钟立主编, 江钟立主编, 江钟立) (Z-Library)/tmp-convert-175318645594011.json",
            "李晓捷版": "人体发育学粗大运动 第2版 (李晓捷主编, 主编李晓捷, 李晓捷, 李晓捷主编, 李晓捷) (Z-Library)/tmp-convert-17531864008521732.json",
            "左天香版": "人体发育学粗大运动 (左天香，徐冬晨主编, 左天香, 徐冬晨主编, 左天香, 徐冬晨) (Z-Library)/tmp-convert-17531868070407812.json",
            "习题集": "《人体发育学学习指导及习题集》粗大运动 (Pdg2Pic, 陈翔主编；吕智海，李林，李晓捷等编) (Z-Library)-compressed/tmp-convert-17531873488369693.json"
        }
        
        loaded_data = {}
        for name, file_path in json_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    loaded_data[name] = data
                    print(f"✅ 成功加载 {name}: {data.get('success_count', 0)} 页")
            except Exception as e:
                print(f"❌ 加载 {name} 失败: {e}")
        
        return loaded_data
    
    def extract_text_content(self, json_data: Dict) -> List[str]:
        """从JSON数据中提取文本内容"""
        all_texts = []
        
        if 'pages' in json_data:
            for page in json_data['pages']:
                if 'content' in page:
                    page_texts = []
                    for content in page['content']:
                        if 'text' in content and content['text'].strip():
                            page_texts.append(content['text'].strip())
                    
                    # 将页面文本合并
                    if page_texts:
                        page_text = ' '.join(page_texts)
                        all_texts.append(page_text)
        
        return all_texts
    
    def extract_reflexes_info(self, texts: List[str]) -> Dict:
        """提取反射相关信息"""
        reflexes = {}
        
        # 反射名称模式
        reflex_patterns = [
            r'(\w+反射).*?(\d+[~～-]\d+个?月)',
            r'(\w+反射).*?存在时期.*?(\d+[~～-]\d+个?月)',
            r'(\w+反射).*?持续.*?(\d+[~～-]\d+个?月)'
        ]
        
        for text in texts:
            for pattern in reflex_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    reflex_name = match[0]
                    time_period = match[1]
                    
                    if reflex_name not in reflexes:
                        reflexes[reflex_name] = {
                            'name': reflex_name,
                            'time_period': time_period,
                            'descriptions': []
                        }
                    
                    # 提取描述信息
                    reflex_context = self._extract_context(text, reflex_name, 100)
                    if reflex_context:
                        reflexes[reflex_name]['descriptions'].append(reflex_context)
        
        return reflexes
    
    def extract_milestones_info(self, texts: List[str]) -> Dict:
        """提取里程碑信息"""
        milestones = {}
        
        # 里程碑模式
        milestone_patterns = [
            r'(\d+)个月.*?(抬头|翻身|坐|爬|站|走|跳)',
            r'(\d+)个月.*?(独\w+|扶\w+|会\w+)',
            r'(\d+)个月.*?(可以|能够|开始).*?(\w+)'
        ]
        
        for text in texts:
            for pattern in milestone_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) >= 2:
                        age = match[0]
                        skill = match[1] if len(match) == 2 else match[1] + match[2]
                        
                        if age not in milestones:
                            milestones[age] = []
                        
                        milestone_context = self._extract_context(text, f"{age}个月", 80)
                        milestones[age].append({
                            'skill': skill,
                            'context': milestone_context
                        })
        
        return milestones
    
    def _extract_context(self, text: str, keyword: str, context_length: int = 100) -> str:
        """提取关键词周围的上下文"""
        index = text.find(keyword)
        if index == -1:
            return ""
        
        start = max(0, index - context_length // 2)
        end = min(len(text), index + len(keyword) + context_length // 2)
        
        return text[start:end].strip()
    
    def generate_qa_pairs_with_llm(self, knowledge_base: Dict) -> List[Dict]:
        """使用LLM生成问答对"""
        qa_pairs = []
        
        system_prompt = """你是一位专业的儿童发育专家。基于提供的专业知识，生成高质量的问答对，用于训练婴幼儿粗大运动发展指导的AI模型。

要求：
1. 问题要贴近实际应用场景，如家长咨询、专业评估等
2. 答案要专业准确，包含具体的指导建议
3. 涵盖0-3岁年龄段的各个发育阶段
4. 每个回答都要包含安全提醒和个体差异说明"""
        
        # 处理反射信息
        for reflex_name, reflex_info in knowledge_base.get('reflexes', {}).items():
            prompt = f"""基于以下反射信息生成3个问答对：
反射名称：{reflex_name}
存在时期：{reflex_info.get('time_period', '')}
描述：{'; '.join(reflex_info.get('descriptions', [])[:2])}

请生成关于这个反射的评估、异常表现、临床意义等方面的问答对。"""
            
            try:
                response = self.qwen_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_qa_response(response)
                    qa_pairs.extend(parsed_qa)
            except Exception as e:
                print(f"生成{reflex_name}问答对时出错: {e}")
        
        # 处理里程碑信息
        for age, milestones in knowledge_base.get('milestones', {}).items():
            if milestones:
                milestone_text = '; '.join([m['skill'] for m in milestones[:3]])
                prompt = f"""基于以下{age}个月的发育里程碑生成3个问答对：
主要技能：{milestone_text}

请生成关于这个年龄段发育评估、指导建议、异常识别等方面的问答对。"""
                
                try:
                    response = self.qwen_client.generate_text(prompt, system_prompt)
                    if response:
                        parsed_qa = self._parse_qa_response(response)
                        qa_pairs.extend(parsed_qa)
                except Exception as e:
                    print(f"生成{age}个月里程碑问答对时出错: {e}")
        
        return qa_pairs
    
    def _parse_qa_response(self, response: str) -> List[Dict]:
        """解析LLM生成的问答对"""
        qa_pairs = []
        
        # 尝试不同的分割模式
        patterns = [
            r'问题?\s*[:：]\s*(.*?)\s*答案?\s*[:：]\s*(.*?)(?=问题?[:：]|$)',
            r'Q\s*[:：]\s*(.*?)\s*A\s*[:：]\s*(.*?)(?=Q[:：]|$)',
            r'(\d+)\.\s*问题?\s*[:：]\s*(.*?)\s*答案?\s*[:：]\s*(.*?)(?=\d+\.|$)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            if matches:
                for match in matches:
                    if len(match) == 2:
                        question, answer = match
                    elif len(match) == 3:
                        _, question, answer = match
                    else:
                        continue
                    
                    question = question.strip()
                    answer = answer.strip()
                    
                    if len(question) > 10 and len(answer) > 20:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer
                        })
                break
        
        return qa_pairs
    
    def process_all_data(self) -> List[Dict]:
        """处理所有数据生成完整数据集"""
        print("🚀 开始处理JSON文献数据...")
        
        # 1. 加载JSON文件
        json_data = self.load_json_files()
        
        # 2. 提取文本内容
        all_texts = []
        for name, data in json_data.items():
            texts = self.extract_text_content(data)
            all_texts.extend(texts)
            print(f"📄 从{name}提取了{len(texts)}段文本")
        
        # 3. 提取结构化知识
        print("🔍 提取反射信息...")
        reflexes = self.extract_reflexes_info(all_texts)
        self.extracted_knowledge['reflexes'] = reflexes
        print(f"✅ 提取了{len(reflexes)}个反射")
        
        print("🎯 提取里程碑信息...")
        milestones = self.extract_milestones_info(all_texts)
        self.extracted_knowledge['milestones'] = milestones
        print(f"✅ 提取了{len(milestones)}个年龄段的里程碑")
        
        # 4. 使用LLM生成问答对
        print("🤖 使用LLM生成专业问答对...")
        qa_pairs = self.generate_qa_pairs_with_llm(self.extracted_knowledge)
        print(f"✅ 生成了{len(qa_pairs)}个问答对")
        
        # 5. 转换为数据集格式
        dataset_entries = []
        for qa in qa_pairs:
            entry = self.schema.create_generic_entry(
                input_text=qa['question'],
                output_text=qa['answer'],
                interaction_type="guidance"
            )
            dataset_entries.append(entry)
        
        return dataset_entries
    
    def save_knowledge_base(self, filename: str = "extracted_knowledge.json"):
        """保存提取的知识库"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.extracted_knowledge, f, ensure_ascii=False, indent=2)
        print(f"📚 知识库已保存到: {filename}")
    
    def generate_enhanced_dataset(self, base_count: int = 100) -> None:
        """生成增强的数据集"""
        print("🎯 开始生成基于文献的增强数据集...")
        
        # 处理JSON数据
        literature_entries = self.process_all_data()
        
        # 保存知识库
        self.save_knowledge_base()
        
        # 添加到schema
        self.schema.entries.extend(literature_entries)
        
        # 生成基础数据补充
        from data_generator import MotorDevelopmentDataGenerator
        base_generator = MotorDevelopmentDataGenerator()
        
        print("📊 生成基础数据补充...")
        base_generator.generate_complete_dataset(
            assessment_count=base_count,
            guidance_count=base_count,
            milestone_count=base_count//2
        )
        
        # 合并数据
        with open("gross_motor_dataset_generated.jsonl", 'r', encoding='utf-8') as f:
            base_data = [json.loads(line) for line in f]
        
        # 转换文献数据为JSONL格式
        literature_data = []
        for entry in literature_entries:
            literature_data.append({
                "instruction": entry.input_text,
                "output": entry.output_text,
                "age_group": entry.age_group.value if entry.age_group else "",
                "interaction_type": entry.interaction_type.value if entry.interaction_type else "",
                "source": "literature",
                "metadata": entry.metadata
            })
        
        # 合并并去重
        all_data = base_data + literature_data
        unique_data = []
        seen = set()
        
        for item in all_data:
            key = (item['instruction'], item['output'])
            if key not in seen:
                seen.add(key)
                unique_data.append(item)
        
        # 保存增强数据集
        output_file = "gross_motor_enhanced_dataset.jsonl"
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in unique_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # 导出其他格式
        alpaca_data = []
        for item in unique_data:
            alpaca_data.append({
                "instruction": item["instruction"],
                "input": "",
                "output": item["output"]
            })
        
        with open("gross_motor_enhanced_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        print(f"🎉 增强数据集生成完成！")
        print(f"📊 总计: {len(unique_data)} 条数据")
        print(f"📄 文献数据: {len(literature_data)} 条")
        print(f"📄 基础数据: {len(base_data)} 条")
        print(f"💾 保存到: {output_file}")

if __name__ == "__main__":
    processor = JSONDataProcessor()
    processor.generate_enhanced_dataset(base_count=150)
