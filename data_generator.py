"""
婴幼儿粗大运动发展指导数据生成器
基于现有资料和专业知识生成高质量训练数据
"""

import json
import pandas as pd
import random
from typing import List, Dict, Tuple
from dataset_schema import DatasetSchema, MotorSkillCategory, AgeGroup, InteractionType

class MotorDevelopmentDataGenerator:
    """粗大运动发展数据生成器"""
    
    def __init__(self):
        self.schema = DatasetSchema()
        self.load_source_data()
        
    def load_source_data(self):
        """加载源数据"""
        # 从XLSX评估量表中提取的里程碑数据
        self.milestones_data = {
            1: ["抬肩坐起头竖直片刻", "俯卧头部翘动"],
            2: ["拉腕坐起头竖直短时", "俯卧头抬离床面"],
            3: ["抱直头稳", "俯卧抬头45°"],
            4: ["扶腋可站片刻", "俯卧抬头90°"],
            5: ["轻拉腕部即坐起", "独坐头身前倾"],
            6: ["仰卧翻身", "独坐直"],
            7: ["悬垂落地姿势", "独坐自如"],
            8: ["双手扶物可站立", "会爬"],
            9: ["拉双手会走", "保护性支撑"],
            10: ["自己坐起", "拇食指动作熟练"],
            11: ["独站片刻", "扶物下蹲取物"],
            12: ["独走自如", "牵一手可走"],
            15: ["脚尖走", "扶楼梯上楼"],
            18: ["独自上楼", "独自下楼"],
            24: ["双足跳离地面", "独脚站2s"],
            30: ["立定跳远", "独脚站10s"],
            36: ["双脚交替跳", "单脚跳"]
        }
        
        # 常见发育问题和指导建议
        self.common_concerns = {
            "头部控制": {
                "问题": ["宝宝头部不稳", "抬头困难", "头总是偏向一侧"],
                "指导": ["增加俯卧时间", "用玩具吸引抬头", "按摩颈部肌肉", "避免长时间仰卧"]
            },
            "翻身": {
                "问题": ["不会翻身", "只会单侧翻身", "翻身动作不协调"],
                "指导": ["协助练习翻身动作", "在侧方放置玩具吸引", "确保安全的练习环境"]
            },
            "坐位": {
                "问题": ["坐不稳", "需要支撑才能坐", "坐位时身体前倾"],
                "指导": ["逐渐减少支撑", "加强核心肌群训练", "提供适当的坐位支撑"]
            },
            "爬行": {
                "问题": ["不会爬行", "只会后退爬", "爬行姿势异常"],
                "指导": ["创造爬行动机", "示范正确爬行姿势", "提供足够的练习空间"]
            },
            "站立": {
                "问题": ["不敢站立", "站立不稳", "足尖着地"],
                "指导": ["逐步增加站立时间", "提供安全的支撑", "鼓励光脚练习"]
            },
            "行走": {
                "问题": ["走路不稳", "容易摔倒", "步态异常"],
                "指导": ["提供安全的行走环境", "鼓励多练习", "注意鞋子的选择"]
            }
        }
    
    def generate_assessment_data(self, count: int = 100) -> List[Dict]:
        """生成评估类数据"""
        assessment_data = []
        
        for _ in range(count):
            # 随机选择年龄
            age_months = random.randint(1, 36)
            age_group = self.schema._get_age_group(age_months)
            
            # 获取该年龄段的里程碑
            expected_milestones = []
            for milestone_age, skills in self.milestones_data.items():
                if milestone_age <= age_months:
                    expected_milestones.extend(skills)
            
            # 随机生成发育状况
            dev_status = random.choice(["正常", "稍有延迟", "明显延迟", "超前"])
            
            if dev_status == "正常":
                achieved = random.sample(expected_milestones, 
                                       min(len(expected_milestones), random.randint(2, 4)))
                assessment = f"宝宝发育正常，已掌握{len(achieved)}项里程碑技能"
                recommendations = ["继续保持良好的发育环境", "定期观察发育进展"]
                
            elif dev_status == "稍有延迟":
                achieved = random.sample(expected_milestones, 
                                       max(1, len(expected_milestones) // 2))
                missing = [skill for skill in expected_milestones if skill not in achieved]
                assessment = f"发育稍有延迟，还需要加强练习"
                recommendations = [f"重点练习{random.choice(missing)}", "增加相关活动时间"]
                
            elif dev_status == "明显延迟":
                achieved = random.sample(expected_milestones, 
                                       max(1, len(expected_milestones) // 3))
                assessment = "发育明显延迟，建议寻求专业评估"
                recommendations = ["尽快咨询儿科医生", "进行专业发育评估", "制定个性化训练计划"]
                
            else:  # 超前
                achieved = expected_milestones.copy()
                if age_months < 36:
                    next_age_skills = self.milestones_data.get(age_months + 6, [])
                    achieved.extend(random.sample(next_age_skills, 
                                                min(len(next_age_skills), 2)))
                assessment = "发育超前，表现优秀"
                recommendations = ["继续提供丰富的运动环境", "注意安全防护"]
            
            # 生成观察行为描述
            behaviors = [f"能够{skill}" if skill in achieved else f"尚未掌握{skill}" 
                        for skill in random.sample(expected_milestones, 
                                                  min(len(expected_milestones), 3))]
            
            # 创建输入输出文本
            input_text = f"我的宝宝{age_months}个月了，观察到的行为：{'; '.join(behaviors)}。请帮忙评估一下发育情况。"
            output_text = f"评估结果：{assessment}\n\n建议：\n" + "\n".join([f"• {rec}" for rec in recommendations])
            
            assessment_data.append({
                "input": input_text,
                "output": output_text,
                "age_months": age_months,
                "interaction_type": "assessment"
            })
        
        return assessment_data
    
    def generate_guidance_data(self, count: int = 100) -> List[Dict]:
        """生成指导类数据"""
        guidance_data = []
        
        for _ in range(count):
            # 随机选择技能和问题
            skill_category = random.choice(list(self.common_concerns.keys()))
            concern_info = self.common_concerns[skill_category]
            
            age_months = random.randint(1, 36)
            problem = random.choice(concern_info["问题"])
            guidance_methods = random.sample(concern_info["指导"], 
                                           min(len(concern_info["指导"]), 3))
            
            # 生成具体的指导建议
            detailed_guidance = []
            for method in guidance_methods:
                if "俯卧时间" in method:
                    detailed_guidance.append(f"{method}：每天2-3次，每次5-10分钟")
                elif "玩具" in method:
                    detailed_guidance.append(f"{method}：使用色彩鲜艳、有声音的玩具")
                elif "练习" in method:
                    detailed_guidance.append(f"{method}：每天练习2-3次，循序渐进")
                else:
                    detailed_guidance.append(method)
            
            # 添加安全提醒
            safety_notes = [
                "确保练习环境安全",
                "避免强迫宝宝做不愿意的动作",
                "如有异常情况及时停止并咨询医生"
            ]
            
            input_text = f"我的宝宝{age_months}个月，{problem}，应该怎么办？"
            output_text = f"针对{skill_category}发育的指导建议：\n\n"
            output_text += "\n".join([f"• {guidance}" for guidance in detailed_guidance])
            output_text += f"\n\n安全提醒：\n" + "\n".join([f"• {note}" for note in safety_notes])
            
            guidance_data.append({
                "input": input_text,
                "output": output_text,
                "age_months": age_months,
                "interaction_type": "guidance"
            })
        
        return guidance_data
    
    def generate_milestone_data(self, count: int = 50) -> List[Dict]:
        """生成里程碑检查数据"""
        milestone_data = []
        
        for age_months, skills in self.milestones_data.items():
            for skill in skills:
                # 生成不同类型的里程碑询问
                question_types = [
                    f"{age_months}个月的宝宝应该会{skill}吗？",
                    f"我的宝宝{age_months}个月了，还不会{skill}，正常吗？",
                    f"如何检查{age_months}个月宝宝是否掌握了{skill}？",
                    f"{skill}这个技能什么时候应该出现？"
                ]
                
                question = random.choice(question_types)
                
                # 生成回答
                answer = f"关于{age_months}个月里程碑'{skill}'：\n\n"
                answer += f"• 预期出现时间：{age_months}个月左右\n"
                answer += f"• 检查方法：观察宝宝在日常活动中是否能够{skill}\n"
                answer += f"• 正常范围：通常在{max(1, age_months-2)}-{age_months+2}个月之间出现\n"
                
                if "头" in skill:
                    answer += "• 促进方法：增加俯卧时间，用玩具吸引抬头\n"
                elif "坐" in skill:
                    answer += "• 促进方法：提供适当支撑，逐渐减少辅助\n"
                elif "爬" in skill:
                    answer += "• 促进方法：创造爬行动机，提供安全空间\n"
                elif "站" in skill or "走" in skill:
                    answer += "• 促进方法：提供支撑练习，鼓励多活动\n"
                
                answer += "• 注意事项：如果超过正常范围仍未出现，建议咨询儿科医生"
                
                milestone_data.append({
                    "input": question,
                    "output": answer,
                    "age_months": age_months,
                    "interaction_type": "milestone"
                })
        
        return milestone_data[:count]
    
    def generate_complete_dataset(self, assessment_count: int = 100, 
                                guidance_count: int = 100, 
                                milestone_count: int = 50) -> None:
        """生成完整数据集"""
        print("开始生成婴幼儿粗大运动发展指导数据集...")
        
        # 生成各类数据
        print("1. 生成评估数据...")
        assessment_data = self.generate_assessment_data(assessment_count)
        
        print("2. 生成指导数据...")
        guidance_data = self.generate_guidance_data(guidance_count)
        
        print("3. 生成里程碑数据...")
        milestone_data = self.generate_milestone_data(milestone_count)
        
        # 合并所有数据
        all_data = assessment_data + guidance_data + milestone_data
        random.shuffle(all_data)  # 随机打乱顺序
        
        # 转换为数据集格式
        print("4. 转换数据格式...")
        for data in all_data:
            entry = self.schema.create_generic_entry(
                input_text=data["input"],
                output_text=data["output"],
                interaction_type=data["interaction_type"],
                age_months=data.get("age_months")
            )
            self.schema.entries.append(entry)
        
        # 导出数据集
        print("5. 导出数据集...")
        self.schema.export_to_jsonl("gross_motor_dataset_generated.jsonl")
        self.schema.export_to_alpaca_format("gross_motor_alpaca_generated.json")
        
        print(f"数据集生成完成！共生成 {len(self.schema.entries)} 条数据")
        
        # 生成统计报告
        self.generate_statistics_report()
    
    def generate_statistics_report(self):
        """生成数据集统计报告"""
        print("\n=== 数据集统计报告 ===")
        
        # 按交互类型统计
        type_counts = {}
        age_counts = {}
        
        for entry in self.schema.entries:
            interaction_type = entry.interaction_type.value if entry.interaction_type else "未知"
            type_counts[interaction_type] = type_counts.get(interaction_type, 0) + 1
            
            age_group = entry.age_group.value if entry.age_group else "未知"
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
        
        print("\n按交互类型分布：")
        for type_name, count in type_counts.items():
            print(f"  {type_name}: {count} 条")
        
        print("\n按年龄组分布：")
        for age_group, count in age_counts.items():
            print(f"  {age_group}: {count} 条")
        
        print(f"\n总计: {len(self.schema.entries)} 条数据")

if __name__ == "__main__":
    generator = MotorDevelopmentDataGenerator()
    generator.generate_complete_dataset(
        assessment_count=150,
        guidance_count=150,
        milestone_count=80
    )
