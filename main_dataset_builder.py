"""
婴幼儿粗大运动发展指导数据集构建主程序
整合所有功能，生成高质量的LLM微调数据集
"""

import os
import json
import argparse
from typing import List, Dict
from data_generator import MotorDevelopmentDataGenerator
from quality_control import DatasetQualityController
from qwen_api_client import QwenAPIClient, QwenConfig, DatasetGenerator

class DatasetBuilder:
    """数据集构建器主类"""
    
    def __init__(self, use_qwen_api: bool = False, api_key: str = None):
        self.use_qwen_api = use_qwen_api
        self.api_key = api_key
        
        # 初始化组件
        self.base_generator = MotorDevelopmentDataGenerator()
        self.quality_controller = DatasetQualityController()
        
        if use_qwen_api and api_key:
            config = QwenConfig(api_key=api_key)
            self.qwen_client = QwenAPIClient(config)
            self.qwen_generator = DatasetGenerator(self.qwen_client)
        else:
            self.qwen_client = None
            self.qwen_generator = None
    
    def build_base_dataset(self, assessment_count: int = 200, 
                          guidance_count: int = 200, 
                          milestone_count: int = 100) -> str:
        """构建基础数据集"""
        print("🚀 开始构建基础数据集...")
        
        self.base_generator.generate_complete_dataset(
            assessment_count=assessment_count,
            guidance_count=guidance_count,
            milestone_count=milestone_count
        )
        
        base_file = "gross_motor_dataset_generated.jsonl"
        print(f"✅ 基础数据集已生成: {base_file}")
        return base_file
    
    def enhance_with_qwen(self, base_file: str, enhancement_count: int = 100) -> str:
        """使用Qwen API增强数据集"""
        if not self.qwen_generator:
            print("❌ Qwen API未配置，跳过增强步骤")
            return base_file
        
        print("🤖 使用Qwen API增强数据集...")
        
        try:
            # 生成增强数据
            self.qwen_generator.generate_dataset(
                assessment_count=enhancement_count//3,
                guidance_count=enhancement_count//3,
                milestone_count=enhancement_count//3
            )
            
            enhanced_file = "gross_motor_dataset_enhanced.jsonl"
            
            # 合并基础数据集和增强数据集
            self._merge_datasets([base_file, "gross_motor_dataset.jsonl"], enhanced_file)
            
            print(f"✅ 增强数据集已生成: {enhanced_file}")
            return enhanced_file
            
        except Exception as e:
            print(f"❌ Qwen API增强失败: {e}")
            return base_file
    
    def _merge_datasets(self, input_files: List[str], output_file: str):
        """合并多个数据集文件"""
        merged_data = []
        
        for file_path in input_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        if file_path.endswith('.jsonl'):
                            for line in f:
                                merged_data.append(json.loads(line))
                        else:
                            data = json.load(f)
                            if isinstance(data, list):
                                merged_data.extend(data)
                            else:
                                merged_data.append(data)
                except Exception as e:
                    print(f"⚠️  读取文件 {file_path} 失败: {e}")
        
        # 去重
        seen = set()
        unique_data = []
        for item in merged_data:
            item_key = (item.get('instruction', ''), item.get('output', ''))
            if item_key not in seen:
                seen.add(item_key)
                unique_data.append(item)
        
        # 保存合并后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in unique_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\\n')
        
        print(f"📊 合并完成: {len(unique_data)} 条唯一数据")
    
    def quality_check(self, file_path: str) -> Dict:
        """质量检查"""
        print(f"🔍 开始质量检查: {file_path}")
        
        report = self.quality_controller.generate_quality_report(file_path)
        self.quality_controller.print_quality_report(report)
        
        # 保存质量报告
        report_file = file_path.replace('.jsonl', '_quality_report.json')
        try:
            # 处理不可序列化的对象
            serializable_report = self._make_serializable(report)
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_report, f, ensure_ascii=False, indent=2)
            print(f"📋 质量报告已保存: {report_file}")
        except Exception as e:
            print(f"⚠️  保存质量报告失败: {e}")
        
        return report
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, set) or isinstance(obj, frozenset):
            return list(obj)
        else:
            return obj
    
    def export_final_formats(self, source_file: str):
        """导出最终格式"""
        print("📤 导出最终数据集格式...")
        
        # 读取源数据
        data = []
        with open(source_file, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line))
        
        # 导出Alpaca格式
        alpaca_data = []
        for item in data:
            alpaca_item = {
                "instruction": item.get("instruction", ""),
                "input": "",
                "output": item.get("output", "")
            }
            alpaca_data.append(alpaca_item)
        
        alpaca_file = source_file.replace('.jsonl', '_alpaca.json')
        with open(alpaca_file, 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        # 导出ChatML格式
        chatml_data = []
        for item in data:
            chatml_item = {
                "messages": [
                    {"role": "user", "content": item.get("instruction", "")},
                    {"role": "assistant", "content": item.get("output", "")}
                ]
            }
            chatml_data.append(chatml_item)
        
        chatml_file = source_file.replace('.jsonl', '_chatml.jsonl')
        with open(chatml_file, 'w', encoding='utf-8') as f:
            for item in chatml_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\\n')
        
        print(f"✅ Alpaca格式: {alpaca_file}")
        print(f"✅ ChatML格式: {chatml_file}")
        print(f"✅ 原始JSONL格式: {source_file}")
    
    def build_complete_dataset(self, config: Dict) -> str:
        """构建完整数据集"""
        print("🎯 开始构建完整的婴幼儿粗大运动发展指导数据集")
        print("=" * 60)
        
        # 1. 构建基础数据集
        base_file = self.build_base_dataset(
            assessment_count=config.get('assessment_count', 200),
            guidance_count=config.get('guidance_count', 200),
            milestone_count=config.get('milestone_count', 100)
        )
        
        # 2. 使用Qwen API增强（如果配置了）
        if self.use_qwen_api:
            enhanced_file = self.enhance_with_qwen(
                base_file, 
                enhancement_count=config.get('enhancement_count', 100)
            )
            final_file = enhanced_file
        else:
            final_file = base_file
        
        # 3. 质量检查
        quality_report = self.quality_check(final_file)
        
        # 4. 导出多种格式
        self.export_final_formats(final_file)
        
        # 5. 生成总结报告
        self._generate_summary_report(final_file, quality_report, config)
        
        print("\\n🎉 数据集构建完成！")
        return final_file
    
    def _generate_summary_report(self, final_file: str, quality_report: Dict, config: Dict):
        """生成总结报告"""
        print("\\n" + "=" * 60)
        print("📊 数据集构建总结报告")
        print("=" * 60)
        
        # 统计信息
        total_entries = quality_report['completeness']['total_entries']
        overall_score = quality_report['overall_score']
        
        print(f"\\n📈 数据集规模:")
        print(f"  总条目数: {total_entries}")
        print(f"  评估类数据: {config.get('assessment_count', 0)} 条")
        print(f"  指导类数据: {config.get('guidance_count', 0)} 条")
        print(f"  里程碑数据: {config.get('milestone_count', 0)} 条")
        
        if self.use_qwen_api:
            print(f"  API增强数据: {config.get('enhancement_count', 0)} 条")
        
        print(f"\\n🏆 质量评估:")
        print(f"  总体评分: {overall_score:.1f}/100")
        print(f"  内容相关性: {quality_report['relevance']['motor_keyword_coverage']:.1%}")
        print(f"  数据多样性: {quality_report['diversity']['instruction_diversity_ratio']:.1%}")
        
        print(f"\\n📁 输出文件:")
        print(f"  主数据集: {final_file}")
        print(f"  Alpaca格式: {final_file.replace('.jsonl', '_alpaca.json')}")
        print(f"  ChatML格式: {final_file.replace('.jsonl', '_chatml.jsonl')}")
        print(f"  质量报告: {final_file.replace('.jsonl', '_quality_report.json')}")
        
        print(f"\\n💡 使用建议:")
        print(f"  • 适用于0-3岁婴幼儿粗大运动发展指导的LLM微调")
        print(f"  • 支持评估、指导、里程碑检查等多种交互场景")
        print(f"  • 建议结合实际临床数据进一步验证和优化")

def main():
    parser = argparse.ArgumentParser(description='婴幼儿粗大运动发展指导数据集构建器')
    parser.add_argument('--use-qwen', action='store_true', help='使用Qwen API增强数据集')
    parser.add_argument('--api-key', type=str, help='Qwen API密钥')
    parser.add_argument('--assessment-count', type=int, default=200, help='评估类数据数量')
    parser.add_argument('--guidance-count', type=int, default=200, help='指导类数据数量')
    parser.add_argument('--milestone-count', type=int, default=100, help='里程碑数据数量')
    parser.add_argument('--enhancement-count', type=int, default=100, help='API增强数据数量')
    
    args = parser.parse_args()
    
    # 从环境变量获取API密钥（如果未通过参数提供）
    api_key = args.api_key or os.getenv('QWEN_API_KEY')
    
    # 创建数据集构建器
    builder = DatasetBuilder(use_qwen_api=args.use_qwen, api_key=api_key)
    
    # 配置参数
    config = {
        'assessment_count': args.assessment_count,
        'guidance_count': args.guidance_count,
        'milestone_count': args.milestone_count,
        'enhancement_count': args.enhancement_count
    }
    
    # 构建数据集
    final_file = builder.build_complete_dataset(config)
    
    print(f"\\n🎯 最终数据集文件: {final_file}")

if __name__ == "__main__":
    main()
