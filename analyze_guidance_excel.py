"""
分析婴幼儿粗大动作指导.xlsx文件
提取其中的发育指导信息，并与现有数据集进行对比
"""

import pandas as pd
import json
from typing import Dict, List

def analyze_guidance_excel():
    """分析婴幼儿粗大动作指导Excel文件"""
    print("📊 分析婴幼儿粗大动作指导.xlsx文件")
    print("=" * 60)
    
    try:
        # 读取Excel文件
        file_path = '婴幼儿粗大动作指导.xlsx'
        df = pd.read_excel(file_path)
        
        print(f"📄 文件基本信息:")
        print(f"  - 行数: {len(df)}")
        print(f"  - 列数: {len(df.columns)}")
        print(f"  - 列名: {list(df.columns)}")
        
        # 显示完整数据
        print(f"\n📋 完整数据内容:")
        print("-" * 40)
        
        # 重新命名列以便处理
        if len(df.columns) >= 2:
            df.columns = ['月龄', '粗大动作描述']
        
        # 提取有效的发育数据
        guidance_data = {}
        
        for index, row in df.iterrows():
            age = row.iloc[0]  # 第一列
            description = row.iloc[1]  # 第二列
            
            # 跳过空值和标题行
            if pd.isna(age) or pd.isna(description):
                continue
            if str(age).strip() in ['月龄/月', '月龄', '']:
                continue
            
            # 尝试提取月龄数字
            try:
                if isinstance(age, (int, float)):
                    age_num = int(age)
                elif isinstance(age, str):
                    # 提取数字
                    import re
                    age_match = re.search(r'(\d+)', str(age))
                    if age_match:
                        age_num = int(age_match.group(1))
                    else:
                        continue
                else:
                    continue
                
                # 清理描述文本
                desc_clean = str(description).strip()
                if desc_clean and len(desc_clean) > 3:
                    guidance_data[age_num] = desc_clean
                    print(f"  {age_num}个月: {desc_clean}")
                    
            except (ValueError, AttributeError):
                continue
        
        print(f"\n✅ 成功提取 {len(guidance_data)} 个月龄的发育指导数据")
        
        # 保存提取的数据
        with open("guidance_excel_data.json", 'w', encoding='utf-8') as f:
            json.dump(guidance_data, f, ensure_ascii=False, indent=2)
        
        return guidance_data
        
    except Exception as e:
        print(f"❌ 分析Excel文件时出错: {e}")
        return {}

def compare_with_existing_data(guidance_data: Dict):
    """与现有数据集进行对比"""
    print(f"\n🔍 与现有数据集进行对比分析")
    print("=" * 60)
    
    # 加载现有的里程碑数据（从data_generator.py中提取）
    existing_milestones = {
        1: ["抬肩坐起头竖直片刻", "俯卧头部翘动"],
        2: ["拉腕坐起头竖直短时", "俯卧头抬离床面"],
        3: ["抱直头稳", "俯卧抬头45°"],
        4: ["扶腋可站片刻", "俯卧抬头90°"],
        5: ["轻拉腕部即坐起", "独坐头身前倾"],
        6: ["仰卧翻身", "独坐直"],
        7: ["悬垂落地姿势", "独坐自如"],
        8: ["双手扶物可站立", "会爬"],
        9: ["拉双手会走", "保护性支撑"],
        10: ["自己坐起", "拇食指动作熟练"],
        11: ["独站片刻", "扶物下蹲取物"],
        12: ["独走自如", "牵一手可走"],
        15: ["脚尖走", "扶楼梯上楼"],
        18: ["独自上楼", "独自下楼"],
        24: ["双足跳离地面", "独脚站2s"],
        30: ["立定跳远", "独脚站10s"],
        36: ["双脚交替跳", "单脚跳"]
    }
    
    print("📊 数据对比结果:")
    print("-" * 40)
    
    # 分析覆盖情况
    guidance_ages = set(guidance_data.keys())
    existing_ages = set(existing_milestones.keys())
    
    print(f"新指导文件覆盖月龄: {sorted(guidance_ages)}")
    print(f"现有数据集覆盖月龄: {sorted(existing_ages)}")
    
    # 找出新增的月龄
    new_ages = guidance_ages - existing_ages
    missing_ages = existing_ages - guidance_ages
    common_ages = guidance_ages & existing_ages
    
    print(f"\n🆕 新指导文件独有的月龄: {sorted(new_ages)}")
    print(f"📋 现有数据集独有的月龄: {sorted(missing_ages)}")
    print(f"🔄 共同覆盖的月龄: {sorted(common_ages)}")
    
    # 详细对比共同月龄的内容
    print(f"\n📝 内容详细对比:")
    print("-" * 40)
    
    for age in sorted(common_ages):
        print(f"\n{age}个月:")
        print(f"  新指导: {guidance_data[age]}")
        print(f"  现有数据: {existing_milestones[age]}")
        
        # 简单的相似性分析
        guidance_text = guidance_data[age].lower()
        existing_text = ' '.join(existing_milestones[age]).lower()
        
        # 检查关键词重叠
        guidance_keywords = set(guidance_text.replace('°', '').split())
        existing_keywords = set(existing_text.split())
        common_keywords = guidance_keywords & existing_keywords
        
        if common_keywords:
            print(f"  共同关键词: {common_keywords}")
        else:
            print(f"  ⚠️ 内容差异较大，可能是互补信息")
    
    return {
        'new_ages': new_ages,
        'missing_ages': missing_ages,
        'common_ages': common_ages,
        'guidance_data': guidance_data,
        'existing_data': existing_milestones
    }

def generate_enhancement_suggestions(comparison_result: Dict):
    """生成数据集增强建议"""
    print(f"\n💡 数据集增强建议")
    print("=" * 60)
    
    new_ages = comparison_result['new_ages']
    guidance_data = comparison_result['guidance_data']
    
    suggestions = []
    
    if new_ages:
        print(f"🎯 建议1: 添加新月龄的发育指导")
        print(f"  可以为以下月龄添加新的里程碑数据:")
        for age in sorted(new_ages):
            print(f"    {age}个月: {guidance_data[age]}")
            suggestions.append({
                'type': 'new_milestone',
                'age': age,
                'description': guidance_data[age]
            })
    
    print(f"\n🎯 建议2: 丰富现有月龄的描述")
    print(f"  新指导文件提供了更详细的描述，可以用来:")
    print(f"    - 生成更多样化的问答对")
    print(f"    - 提供更具体的发育指导")
    print(f"    - 增加训练数据的专业性")
    
    print(f"\n🎯 建议3: 创建基于新指导的LLM增强数据")
    print(f"  可以使用新的指导内容作为LLM的输入，生成:")
    print(f"    - 更详细的发育评估问答")
    print(f"    - 针对性的训练指导")
    print(f"    - 家长常见问题的专业回答")
    
    return suggestions

def create_enhanced_data_generator(guidance_data: Dict):
    """创建基于新指导数据的增强生成器"""
    print(f"\n🚀 创建增强数据生成器")
    print("=" * 60)
    
    enhanced_generator_code = f'''"""
基于婴幼儿粗大动作指导的增强数据生成器
使用新的指导数据生成更专业的训练数据
"""

import random
from typing import List, Dict

class GuidanceEnhancedGenerator:
    """基于指导数据的增强生成器"""
    
    def __init__(self):
        # 从Excel文件提取的指导数据
        self.guidance_data = {guidance_data}
    
    def generate_guidance_qa_pairs(self, count: int = 50) -> List[Dict]:
        """基于指导数据生成问答对"""
        qa_pairs = []
        
        question_templates = [
            "我的宝宝{{age}}个月了，这个阶段的粗大运动发育应该是什么样的？",
            "{{age}}个月的宝宝在粗大运动方面有什么特点？",
            "如何判断{{age}}个月宝宝的粗大运动发育是否正常？",
            "{{age}}个月宝宝的粗大运动训练重点是什么？",
            "我家宝宝{{age}}个月，{{description}}，这正常吗？"
        ]
        
        for age, description in self.guidance_data.items():
            for template in question_templates:
                if len(qa_pairs) >= count:
                    break
                    
                question = template.format(age=age, description=description[:20])
                
                answer = f"{{age}}个月宝宝粗大运动发育指导：\\n\\n"
                answer += f"• 发育特点：{{description}}\\n"
                answer += f"• 观察要点：注意宝宝是否能够完成相应的动作\\n"
                answer += f"• 训练建议：提供适当的练习机会和安全环境\\n"
                answer += f"• 个体差异：每个宝宝发育进度略有不同，前后1-2个月都属正常\\n"
                answer += f"\\n⚠️ 如有发育异常或疑虑，请及时咨询专业医生。"
                
                qa_pairs.append({{
                    'question': question,
                    'answer': answer,
                    'age': age,
                    'source': 'guidance_enhanced'
                }})
        
        return qa_pairs[:count]
'''
    
    # 保存增强生成器代码
    with open("guidance_enhanced_generator.py", 'w', encoding='utf-8') as f:
        f.write(enhanced_generator_code)
    
    print("✅ 增强生成器代码已保存到: guidance_enhanced_generator.py")

def main():
    """主函数"""
    print("🔍 婴幼儿粗大动作指导Excel文件分析")
    print("=" * 80)
    
    # 1. 分析Excel文件
    guidance_data = analyze_guidance_excel()
    
    if not guidance_data:
        print("❌ 无法提取有效数据，分析终止")
        return
    
    # 2. 与现有数据对比
    comparison_result = compare_with_existing_data(guidance_data)
    
    # 3. 生成增强建议
    suggestions = generate_enhancement_suggestions(comparison_result)
    
    # 4. 创建增强生成器
    create_enhanced_data_generator(guidance_data)
    
    print(f"\n🎉 分析完成！")
    print(f"📊 发现了 {len(guidance_data)} 个月龄的新指导数据")
    print(f"💡 生成了 {len(suggestions)} 个增强建议")
    print(f"🚀 创建了增强数据生成器")

if __name__ == "__main__":
    main()
