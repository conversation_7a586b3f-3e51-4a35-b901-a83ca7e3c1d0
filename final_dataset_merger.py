"""
最终数据集合并器
将基于文献的专业数据与基础生成数据合并，创建完整的训练数据集
"""

import json
import random
from typing import List, Dict, Set
from quality_control import DatasetQualityController

class FinalDatasetMerger:
    """最终数据集合并器"""
    
    def __init__(self):
        self.quality_controller = DatasetQualityController()
    
    def load_datasets(self) -> Dict[str, List[Dict]]:
        """加载所有数据集"""
        datasets = {}
        
        # 加载基础生成的数据集
        try:
            with open("gross_motor_dataset_generated.jsonl", 'r', encoding='utf-8') as f:
                base_data = [json.loads(line) for line in f]
                datasets['base'] = base_data
                print(f"✅ 加载基础数据集: {len(base_data)} 条")
        except FileNotFoundError:
            print("❌ 基础数据集不存在，将只使用文献数据")
            datasets['base'] = []
        
        # 加载基于文献的数据集
        try:
            with open("literature_based_dataset.jsonl", 'r', encoding='utf-8') as f:
                literature_data = [json.loads(line) for line in f]
                datasets['literature'] = literature_data
                print(f"✅ 加载文献数据集: {len(literature_data)} 条")
        except FileNotFoundError:
            print("❌ 文献数据集不存在")
            datasets['literature'] = []
        
        return datasets
    
    def clean_literature_data(self, literature_data: List[Dict]) -> List[Dict]:
        """清理文献数据中的问题"""
        cleaned_data = []
        
        for item in literature_data:
            # 跳过明显有问题的反射名称
            instruction = item.get('instruction', '')
            
            # 过滤掉过长或不合理的反射名称
            problematic_patterns = [
                '因为获得新的运动能力必须首先抑制原始反射',
                '头和躯干的分离运动对称性紧张性颈反射',
                '时该反射',
                '伸展的反射'
            ]
            
            skip_item = False
            for pattern in problematic_patterns:
                if pattern in instruction:
                    skip_item = True
                    break
            
            if skip_item:
                continue
            
            # 清理和改进问题表述
            if '反射' in instruction:
                # 改进反射相关问题的表述
                if '异常会有什么表现' in instruction:
                    reflex_name = instruction.split('异常会有什么表现')[0].strip()
                    if len(reflex_name) < 20:  # 只保留合理长度的反射名称
                        item['instruction'] = f"{reflex_name}异常有什么表现？需要注意什么？"
                elif '是什么？什么时候出现和消失' in instruction:
                    reflex_name = instruction.split('是什么？')[0].strip()
                    if len(reflex_name) < 20:
                        item['instruction'] = f"什么是{reflex_name}？什么时候出现和消失？"
                elif '如何检查宝宝的' in instruction and '是否正常' in instruction:
                    reflex_name = instruction.split('如何检查宝宝的')[1].split('是否正常')[0].strip()
                    if len(reflex_name) < 20:
                        item['instruction'] = f"如何检查宝宝的{reflex_name}？"
            
            # 改进里程碑相关问题
            if '个月' in instruction and ('发育' in instruction or '练习' in instruction):
                # 提取年龄信息
                import re
                age_match = re.search(r'(\d+)个月', instruction)
                if age_match:
                    age = int(age_match.group(1))
                    # 只保留0-36个月的数据
                    if 0 <= age <= 36:
                        cleaned_data.append(item)
            elif 'assessment' in item.get('category', ''):
                # 保留评估类数据
                cleaned_data.append(item)
            elif '反射' in instruction and len(instruction) < 100:
                # 保留合理的反射相关数据
                cleaned_data.append(item)
        
        print(f"🧹 文献数据清理: {len(literature_data)} -> {len(cleaned_data)} 条")
        return cleaned_data
    
    def enhance_base_data(self, base_data: List[Dict]) -> List[Dict]:
        """增强基础数据"""
        enhanced_data = []
        
        for item in base_data:
            # 确保数据格式统一
            if 'instruction' in item and 'output' in item:
                enhanced_item = {
                    'instruction': item['instruction'],
                    'output': item['output'],
                    'age_group': item.get('age_group', ''),
                    'interaction_type': item.get('interaction_type', ''),
                    'source': 'base_generated',
                    'category': self._categorize_item(item)
                }
                enhanced_data.append(enhanced_item)
        
        return enhanced_data
    
    def _categorize_item(self, item: Dict) -> str:
        """为基础数据分类"""
        instruction = item.get('instruction', '').lower()
        
        if '评估' in instruction or '发育情况' in instruction:
            return 'assessment'
        elif '里程碑' in instruction or '应该会' in instruction:
            return 'milestone'
        elif '如何' in instruction or '怎么' in instruction:
            return 'guidance'
        else:
            return 'general'
    
    def remove_duplicates(self, all_data: List[Dict]) -> List[Dict]:
        """去除重复数据"""
        seen = set()
        unique_data = []
        
        for item in all_data:
            # 创建基于instruction和output的唯一标识
            key = (item['instruction'][:100], item['output'][:100])  # 使用前100字符避免过长
            
            if key not in seen:
                seen.add(key)
                unique_data.append(item)
        
        print(f"🔄 去重处理: {len(all_data)} -> {len(unique_data)} 条")
        return unique_data
    
    def balance_dataset(self, data: List[Dict]) -> List[Dict]:
        """平衡数据集各类别比例"""
        # 按类别分组
        categories = {}
        for item in data:
            category = item.get('category', 'general')
            if category not in categories:
                categories[category] = []
            categories[category].append(item)
        
        print("📊 数据分布:")
        for category, items in categories.items():
            print(f"  {category}: {len(items)} 条")
        
        # 平衡各类别数据量
        balanced_data = []
        max_per_category = 200  # 每个类别最多200条
        
        for category, items in categories.items():
            if len(items) > max_per_category:
                # 随机采样
                sampled_items = random.sample(items, max_per_category)
                balanced_data.extend(sampled_items)
                print(f"  {category}: 采样 {len(sampled_items)} 条")
            else:
                balanced_data.extend(items)
                print(f"  {category}: 保留 {len(items)} 条")
        
        return balanced_data
    
    def add_professional_qa_pairs(self) -> List[Dict]:
        """添加专业的问答对"""
        professional_qa = [
            {
                'instruction': '0-3个月宝宝的粗大运动发育特点是什么？',
                'output': '0-3个月宝宝粗大运动发育特点：\n\n• 头部控制：从完全无法控制到能短暂抬头\n• 原始反射：多种原始反射存在，如拥抱反射、觅食反射\n• 肌张力：逐渐从屈曲姿势向伸展发展\n• 运动模式：主要是反射性运动，缺乏主动控制\n• 发育重点：建立头部控制是这个阶段的关键\n\n💡 指导要点：多进行俯卧练习，促进头部控制发育。',
                'category': 'milestone',
                'age_group': '0-3个月',
                'source': 'professional'
            },
            {
                'instruction': '4-6个月宝宝应该重点发展哪些粗大运动技能？',
                'output': '4-6个月宝宝粗大运动发育重点：\n\n• 翻身技能：从仰卧翻至俯卧，再从俯卧翻至仰卧\n• 坐位准备：在支撑下能短暂坐立\n• 头部控制：头部控制更加稳定\n• 原始反射：部分原始反射开始消失\n• 主动运动：开始出现更多主动运动\n\n🎯 训练建议：鼓励翻身练习，提供坐位支撑训练。',
                'category': 'milestone',
                'age_group': '4-6个月',
                'source': 'professional'
            },
            {
                'instruction': '7-12个月宝宝粗大运动发育的关键里程碑有哪些？',
                'output': '7-12个月宝宝关键里程碑：\n\n• 7-8个月：独立坐稳，开始爬行\n• 9-10个月：扶站，巡航步行\n• 11-12个月：独立站立，开始独立行走\n• 平衡发展：平衡反应逐渐建立\n• 协调性：手眼协调和全身协调性提高\n\n🚀 发育意义：这是运动技能快速发展的关键期，为独立行走奠定基础。',
                'category': 'milestone',
                'age_group': '7-12个月',
                'source': 'professional'
            },
            {
                'instruction': '13-24个月宝宝的粗大运动发育有什么特点？',
                'output': '13-24个月宝宝发育特点：\n\n• 行走技能：从蹒跚学步到稳定行走\n• 跑跳发展：开始尝试跑步和跳跃\n• 平衡提升：平衡能力显著提高\n• 协调性：动作协调性不断改善\n• 探索欲强：运动技能促进环境探索\n\n⚡ 注意事项：提供安全的运动环境，鼓励多样化的运动体验。',
                'category': 'milestone',
                'age_group': '13-24个月',
                'source': 'professional'
            },
            {
                'instruction': '25-36个月宝宝应该掌握哪些高级粗大运动技能？',
                'output': '25-36个月宝宝高级技能：\n\n• 跳跃技能：双脚跳、单脚跳、立定跳远\n• 平衡技能：单脚站立、走平衡木\n• 球类技能：踢球、接球、投球\n• 攀爬技能：上下楼梯、攀爬设施\n• 复杂动作：跑步转弯、急停急转\n\n🎪 发育意义：为学龄前期更复杂的运动技能做准备。',
                'category': 'milestone',
                'age_group': '25-36个月',
                'source': 'professional'
            }
        ]
        
        return professional_qa
    
    def create_final_dataset(self) -> None:
        """创建最终的完整数据集"""
        print("🎯 开始创建最终数据集...")
        
        # 1. 加载所有数据集
        datasets = self.load_datasets()
        
        # 2. 清理文献数据
        cleaned_literature = self.clean_literature_data(datasets['literature'])
        
        # 3. 增强基础数据
        enhanced_base = self.enhance_base_data(datasets['base'])
        
        # 4. 添加专业问答对
        professional_qa = self.add_professional_qa_pairs()
        
        # 5. 合并所有数据
        all_data = enhanced_base + cleaned_literature + professional_qa
        print(f"📊 合并后总数据: {len(all_data)} 条")
        
        # 6. 去重
        unique_data = self.remove_duplicates(all_data)
        
        # 7. 平衡数据集
        balanced_data = self.balance_dataset(unique_data)
        
        # 8. 随机打乱
        random.shuffle(balanced_data)
        
        # 9. 保存最终数据集
        self._save_final_dataset(balanced_data)
        
        # 10. 质量检查
        self._perform_quality_check(balanced_data)
        
        print("🎉 最终数据集创建完成！")
    
    def _save_final_dataset(self, data: List[Dict]) -> None:
        """保存最终数据集"""
        # JSONL格式
        with open("final_gross_motor_dataset.jsonl", 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Alpaca格式
        alpaca_data = []
        for item in data:
            alpaca_data.append({
                "instruction": item['instruction'],
                "input": "",
                "output": item['output']
            })
        
        with open("final_gross_motor_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        # ChatML格式
        with open("final_gross_motor_chatml.jsonl", 'w', encoding='utf-8') as f:
            for item in data:
                chatml_item = {
                    "messages": [
                        {"role": "user", "content": item['instruction']},
                        {"role": "assistant", "content": item['output']}
                    ]
                }
                f.write(json.dumps(chatml_item, ensure_ascii=False) + '\n')
        
        print(f"💾 最终数据集已保存:")
        print(f"  - JSONL格式: final_gross_motor_dataset.jsonl ({len(data)} 条)")
        print(f"  - Alpaca格式: final_gross_motor_alpaca.json")
        print(f"  - ChatML格式: final_gross_motor_chatml.jsonl")
    
    def _perform_quality_check(self, data: List[Dict]) -> None:
        """执行质量检查"""
        print("\n🔍 执行最终质量检查...")
        
        # 临时保存数据用于质量检查
        temp_file = "temp_final_dataset.jsonl"
        with open(temp_file, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # 执行质量检查
        report = self.quality_controller.generate_quality_report(temp_file)
        self.quality_controller.print_quality_report(report)
        
        # 保存质量报告
        with open("final_dataset_quality_report.json", 'w', encoding='utf-8') as f:
            # 处理不可序列化的对象
            serializable_report = self._make_serializable(report)
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        # 删除临时文件
        import os
        os.remove(temp_file)
        
        print("📋 质量报告已保存: final_dataset_quality_report.json")
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, set) or isinstance(obj, frozenset):
            return list(obj)
        else:
            return obj
    
    def generate_dataset_summary(self, data: List[Dict]) -> None:
        """生成数据集总结"""
        print("\n" + "="*60)
        print("📊 最终数据集总结")
        print("="*60)
        
        # 统计各类别数量
        categories = {}
        age_groups = {}
        sources = {}
        
        for item in data:
            # 按类别统计
            category = item.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
            
            # 按年龄组统计
            age_group = item.get('age_group', 'unknown')
            age_groups[age_group] = age_groups.get(age_group, 0) + 1
            
            # 按来源统计
            source = item.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        print(f"\n📈 总体规模: {len(data)} 条训练数据")
        
        print(f"\n🏷️  按类别分布:")
        for category, count in sorted(categories.items()):
            percentage = count / len(data) * 100
            print(f"  {category}: {count} 条 ({percentage:.1f}%)")
        
        print(f"\n👶 按年龄组分布:")
        for age_group, count in sorted(age_groups.items()):
            percentage = count / len(data) * 100
            print(f"  {age_group}: {count} 条 ({percentage:.1f}%)")
        
        print(f"\n📚 按数据来源分布:")
        for source, count in sorted(sources.items()):
            percentage = count / len(data) * 100
            print(f"  {source}: {count} 条 ({percentage:.1f}%)")

if __name__ == "__main__":
    merger = FinalDatasetMerger()
    merger.create_final_dataset()
