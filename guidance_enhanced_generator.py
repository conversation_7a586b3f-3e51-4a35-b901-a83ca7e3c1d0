"""
基于婴幼儿粗大动作指导的增强数据生成器
使用新的指导数据生成更专业的训练数据
"""

import json
import random
from typing import List, Dict
from qwen_api_client import QwenAPIClient, QwenConfig

class GuidanceEnhancedGenerator:
    """基于指导数据的增强生成器"""

    def __init__(self):
        # 从Excel文件提取的指导数据
        self.guidance_data = {
            1: "俯卧位时能勉强抬头",
            2: "俯卧抬头30°∼45°，直立位时头一晃一晃的，能竖一下",
            3: "俯卧能持久抬头至45°，扶着坐时头向前倾，头稍晃、不稳",
            4: "俯卧抬头90°，扶坐时头稳定",
            5: "能翻身至俯卧位，拉坐时头不后滞",
            6: "拉坐时会主动举头，扶站能主动跳跃",
            7: "俯卧时以腹部为中心做旋转运动，可以独坐1分钟",
            8: "独自坐稳，左右转动自如，扶着栏杆能站立",
            9: "会爬行，并会拉物站起",
            10: "会扶栏杆横走，扶栏杆自己坐下",
            11: "拉着一只手能走，会独自站立片刻",
            12: "能从一个物体到另一个物体走几步（扶物走路），会爬上台阶",
            15: "独走稳，拉着一只手能走上楼梯",
            18: "会举手过肩扔球，会拉着玩具倒退着走，自己扶栏杆走上楼梯",
            24: "跑得好，会双脚并跳，独自上下楼梯，会扔球、踢球",
            28: "单脚站立1秒钟，会跳远",
            34: "单脚站立5秒钟",
            36: "两脚交替上下楼梯，会双脚从末级台阶跳下，能骑小三轮车"
        }

        # 配置LLM客户端
        try:
            config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
            self.llm_client = QwenAPIClient(config)
            self.use_llm = True
        except:
            self.use_llm = False
            print("⚠️ LLM客户端初始化失败，将使用规则生成")

    def generate_guidance_qa_pairs(self, count: int = 50) -> List[Dict]:
        """基于指导数据生成问答对"""
        qa_pairs = []

        question_templates = [
            "我的宝宝{age}个月了，这个阶段的粗大运动发育应该是什么样的？",
            "{age}个月的宝宝在粗大运动方面有什么特点？",
            "如何判断{age}个月宝宝的粗大运动发育是否正常？",
            "{age}个月宝宝的粗大运动训练重点是什么？",
            "我家宝宝{age}个月，{skill_desc}，这正常吗？",
            "宝宝{age}个月了，还不会{skill}，需要担心吗？",
            "如何帮助{age}个月的宝宝练习{skill}？"
        ]

        for age, description in self.guidance_data.items():
            # 提取主要技能
            main_skills = self._extract_main_skills(description)

            for i, template in enumerate(question_templates):
                if len(qa_pairs) >= count:
                    break

                # 根据模板类型选择参数
                if "{skill_desc}" in template:
                    question = template.format(age=age, skill_desc=description[:30])
                elif "{skill}" in template and main_skills:
                    skill = random.choice(main_skills)
                    question = template.format(age=age, skill=skill)
                else:
                    question = template.format(age=age)

                # 生成回答
                answer = self._generate_answer(age, description, template)

                qa_pairs.append({
                    'question': question,
                    'answer': answer,
                    'age': age,
                    'source': 'guidance_enhanced'
                })

        return qa_pairs[:count]

    def _extract_main_skills(self, description: str) -> List[str]:
        """从描述中提取主要技能"""
        skills = []

        # 常见技能关键词
        skill_keywords = [
            "抬头", "翻身", "坐", "爬行", "站立", "走", "跑", "跳",
            "扔球", "踢球", "上楼梯", "下楼梯", "骑车"
        ]

        for keyword in skill_keywords:
            if keyword in description:
                skills.append(keyword)

        return skills if skills else ["相应动作"]

    def _generate_answer(self, age: int, description: str, template: str) -> str:
        """生成回答"""
        base_answer = f"{age}个月宝宝粗大运动发育指导：\n\n"
        base_answer += f"• 发育特点：{description}\n"

        # 根据问题类型添加不同的指导内容
        if "训练重点" in template:
            base_answer += f"• 训练重点：根据发育特点进行针对性练习\n"
            base_answer += f"• 练习方法：创造安全的练习环境，鼓励宝宝主动尝试\n"
        elif "如何判断" in template or "正常吗" in template:
            base_answer += f"• 评估标准：观察宝宝是否能完成相应的动作\n"
            base_answer += f"• 正常范围：发育时间可能前后相差1-2个月\n"
        elif "如何帮助" in template or "练习" in template:
            base_answer += f"• 练习建议：提供适当的练习机会和引导\n"
            base_answer += f"• 注意事项：循序渐进，不要强迫宝宝\n"
        else:
            base_answer += f"• 观察要点：注意宝宝动作的协调性和稳定性\n"
            base_answer += f"• 发育意义：为后续更复杂的运动技能奠定基础\n"

        base_answer += f"• 个体差异：每个宝宝发育进度略有不同，保持耐心很重要\n"
        base_answer += f"\n⚠️ 如有发育异常或疑虑，请及时咨询专业医生。"

        return base_answer

    def generate_llm_enhanced_qa(self, count: int = 30) -> List[Dict]:
        """使用LLM生成增强的问答对"""
        if not self.use_llm:
            print("⚠️ LLM不可用，跳过LLM增强生成")
            return []

        qa_pairs = []

        system_prompt = """你是一位专业的儿童发育专家。基于提供的婴幼儿粗大运动发育指导，生成实用的问答对。

要求：
1. 问题要贴近家长的实际关切，语言自然
2. 回答要专业准确，包含具体的观察要点和指导建议
3. 每个回答都要包含安全提醒和个体差异说明
4. 格式：问题：xxx 回答：xxx"""

        # 选择一些关键月龄进行LLM增强
        key_ages = [3, 6, 9, 12, 18, 24, 36]

        for age in key_ages:
            if age not in self.guidance_data:
                continue

            description = self.guidance_data[age]

            prompt = f"""基于以下{age}个月的粗大运动发育指导，生成2个不同类型的问答对：

发育指导：{description}

请生成：
1. 一个关于发育评估的问答（家长询问是否正常）
2. 一个关于训练指导的问答（家长询问如何促进发育）

每个问答都要专业准确，同时便于家长理解。"""

            try:
                response = self.llm_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_llm_response(response, age)
                    qa_pairs.extend(parsed_qa)
                    print(f"✅ 为{age}个月生成了{len(parsed_qa)}个LLM增强问答对")
            except Exception as e:
                print(f"❌ 生成{age}个月LLM问答对失败: {e}")

        return qa_pairs[:count]

    def _parse_llm_response(self, response: str, age: int) -> List[Dict]:
        """解析LLM响应"""
        qa_pairs = []

        import re

        # 尝试多种解析模式
        patterns = [
            r'问题?\s*[:：]\s*(.*?)\s*回?答案?\s*[:：]\s*(.*?)(?=问题?[:：]|$)',
            r'(\d+)\.\s*问题?\s*[:：]?\s*(.*?)\s*回?答案?\s*[:：]?\s*(.*?)(?=\d+\.|$)',
            r'问：\s*(.*?)\s*答：\s*(.*?)(?=问：|$)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            if matches:
                for match in matches:
                    if len(match) == 2:
                        question, answer = match
                    elif len(match) == 3:
                        _, question, answer = match
                    else:
                        continue

                    question = question.strip()
                    answer = answer.strip()

                    if 10 <= len(question) <= 200 and 20 <= len(answer) <= 1000:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'age': age,
                            'source': 'guidance_llm_enhanced'
                        })
                break

        return qa_pairs

    def generate_comparative_analysis(self, count: int = 20) -> List[Dict]:
        """生成对比分析类问答对"""
        qa_pairs = []

        # 相邻月龄对比
        ages = sorted(self.guidance_data.keys())

        comparative_templates = [
            "{age1}个月和{age2}个月的宝宝在粗大运动发育上有什么区别？",
            "我家宝宝{age1}个月了，什么时候能达到{age2}个月的发育水平？",
            "如何判断宝宝是否从{age1}个月的发育阶段进步到{age2}个月？"
        ]

        for i in range(len(ages) - 1):
            if len(qa_pairs) >= count:
                break

            age1, age2 = ages[i], ages[i + 1]
            desc1, desc2 = self.guidance_data[age1], self.guidance_data[age2]

            for template in comparative_templates:
                if len(qa_pairs) >= count:
                    break

                question = template.format(age1=age1, age2=age2)

                answer = f"{age1}个月与{age2}个月发育对比：\n\n"
                answer += f"• {age1}个月特点：{desc1}\n"
                answer += f"• {age2}个月特点：{desc2}\n"
                answer += f"• 主要进步：从{age1}个月到{age2}个月，宝宝的运动能力会有明显提升\n"
                answer += f"• 发育时间：通常在{age2-1}-{age2+1}个月之间达到{age2}个月的发育水平\n"
                answer += f"• 观察要点：注意动作的稳定性和协调性的改善\n"
                answer += f"\n💡 每个宝宝发育节奏不同，耐心观察和适当引导很重要。"

                qa_pairs.append({
                    'question': question,
                    'answer': answer,
                    'age_range': f"{age1}-{age2}个月",
                    'source': 'guidance_comparative'
                })

        return qa_pairs[:count]

    def generate_complete_enhanced_dataset(self,
                                         guidance_count: int = 50,
                                         llm_count: int = 30,
                                         comparative_count: int = 20) -> List[Dict]:
        """生成完整的增强数据集"""
        print("🚀 开始生成基于指导数据的增强数据集...")

        all_qa_pairs = []

        # 1. 基于指导数据的规则生成
        print("📊 生成基于指导数据的问答对...")
        guidance_qa = self.generate_guidance_qa_pairs(guidance_count)
        all_qa_pairs.extend(guidance_qa)
        print(f"✅ 生成指导问答对: {len(guidance_qa)} 个")

        # 2. LLM增强生成
        print("🤖 生成LLM增强问答对...")
        llm_qa = self.generate_llm_enhanced_qa(llm_count)
        all_qa_pairs.extend(llm_qa)
        print(f"✅ 生成LLM增强问答对: {len(llm_qa)} 个")

        # 3. 对比分析生成
        print("🔍 生成对比分析问答对...")
        comparative_qa = self.generate_comparative_analysis(comparative_count)
        all_qa_pairs.extend(comparative_qa)
        print(f"✅ 生成对比分析问答对: {len(comparative_qa)} 个")

        # 4. 保存数据集
        self._save_enhanced_dataset(all_qa_pairs)

        print(f"\n🎉 基于指导数据的增强数据集生成完成！")
        print(f"📊 总计: {len(all_qa_pairs)} 条高质量数据")

        return all_qa_pairs

    def _save_enhanced_dataset(self, qa_pairs: List[Dict]) -> None:
        """保存增强数据集"""
        # JSONL格式
        with open("guidance_enhanced_dataset.jsonl", 'w', encoding='utf-8') as f:
            for qa in qa_pairs:
                jsonl_entry = {
                    "instruction": qa['question'],
                    "output": qa['answer'],
                    "source": qa.get('source', 'guidance_enhanced'),
                    "age": qa.get('age', ''),
                    "category": "guidance_enhanced"
                }
                f.write(json.dumps(jsonl_entry, ensure_ascii=False) + '\n')

        # Alpaca格式
        alpaca_data = []
        for qa in qa_pairs:
            alpaca_data.append({
                "instruction": qa['question'],
                "input": "",
                "output": qa['answer']
            })

        with open("guidance_enhanced_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)

        print(f"💾 增强数据集已保存:")
        print(f"  - JSONL格式: guidance_enhanced_dataset.jsonl")
        print(f"  - Alpaca格式: guidance_enhanced_alpaca.json")

if __name__ == "__main__":
    generator = GuidanceEnhancedGenerator()
    generator.generate_complete_enhanced_dataset(
        guidance_count=60,
        llm_count=40,
        comparative_count=30
    )
