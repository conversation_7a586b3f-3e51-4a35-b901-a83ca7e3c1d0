"""
婴幼儿粗大运动发展指导数据集架构设计
针对0-3岁婴幼儿粗大运动发展的LLM微调数据集
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Union
from enum import Enum
import json

class AgeGroup(Enum):
    """年龄分组"""
    NEWBORN = "0-1月"      # 新生儿期
    INFANT_1_3 = "1-3月"   # 婴儿早期
    INFANT_4_6 = "4-6月"   # 婴儿中期
    INFANT_7_9 = "7-9月"   # 婴儿晚期
    INFANT_10_12 = "10-12月" # 婴儿末期
    TODDLER_13_18 = "13-18月" # 幼儿早期
    TODDLER_19_24 = "19-24月" # 幼儿中期
    TODDLER_25_36 = "25-36月" # 幼儿晚期

class MotorSkillCategory(Enum):
    """粗大运动技能分类"""
    HEAD_CONTROL = "头部控制"
    ROLLING = "翻身"
    SITTING = "坐位"
    CRAWLING = "爬行"
    STANDING = "站立"
    WALKING = "行走"
    RUNNING = "跑步"
    JUMPING = "跳跃"
    BALANCE = "平衡"
    COORDINATION = "协调性"

class DevelopmentStage(Enum):
    """发育阶段"""
    PRIMITIVE_REFLEXES = "原始反射期"
    POSTURAL_MAINTENANCE = "姿势维持期"
    MOVEMENT_PREPARATION = "移动准备期"
    KNEE_SITTING = "屈膝坐位期"
    KNEE_STANDING = "屈膝站立期"
    BIPEDAL_WALKING = "双足步行期"

class InteractionType(Enum):
    """交互类型"""
    ASSESSMENT = "评估"
    GUIDANCE = "指导"
    MILESTONE_CHECK = "里程碑检查"
    CONCERN_RESPONSE = "问题回应"
    ACTIVITY_SUGGESTION = "活动建议"
    SAFETY_ADVICE = "安全建议"

@dataclass
class MotorMilestone:
    """运动里程碑"""
    age_months: int
    skill_name: str
    description: str
    category: MotorSkillCategory
    is_critical: bool = False
    prerequisites: List[str] = None
    
class DatasetEntry:
    """数据集条目基类"""
    def __init__(self):
        self.entry_id: str = ""
        self.age_group: AgeGroup = None
        self.interaction_type: InteractionType = None
        self.input_text: str = ""
        self.output_text: str = ""
        self.metadata: Dict = {}

@dataclass
class AssessmentEntry(DatasetEntry):
    """评估类数据条目"""
    def __init__(self):
        super().__init__()
        self.child_age_months: int = 0
        self.observed_behaviors: List[str] = None
        self.assessment_result: str = ""
        self.recommendations: List[str] = None
        self.concerns: List[str] = None

@dataclass
class GuidanceEntry(DatasetEntry):
    """指导类数据条目"""
    def __init__(self):
        super().__init__()
        self.target_skill: MotorSkillCategory = None
        self.current_level: str = ""
        self.guidance_steps: List[str] = None
        self.activities: List[str] = None
        self.safety_notes: List[str] = None

@dataclass
class MilestoneEntry(DatasetEntry):
    """里程碑检查类数据条目"""
    def __init__(self):
        super().__init__()
        self.milestone: MotorMilestone = None
        self.achievement_status: str = ""
        self.next_steps: List[str] = None

class DatasetSchema:
    """数据集架构管理器"""
    
    def __init__(self):
        self.entries: List[DatasetEntry] = []
        self.milestones = self._initialize_milestones()
    
    def _initialize_milestones(self) -> List[MotorMilestone]:
        """初始化运动里程碑数据"""
        milestones = [
            # 0-3月里程碑
            MotorMilestone(1, "抬肩坐起头竖直片刻", "仰卧位拉坐时头部能竖直保持2秒以上", MotorSkillCategory.HEAD_CONTROL, True),
            MotorMilestone(2, "俯卧头部翘动", "俯卧位时有头部翘动动作", MotorSkillCategory.HEAD_CONTROL),
            MotorMilestone(3, "俯卧抬头45°", "俯卧位时头部能抬起45度", MotorSkillCategory.HEAD_CONTROL, True),
            
            # 4-6月里程碑
            MotorMilestone(4, "俯卧抬头90°", "俯卧位时头部能抬起90度", MotorSkillCategory.HEAD_CONTROL, True),
            MotorMilestone(5, "轻拉腕部即坐起", "轻拉腕部时能主动坐起", MotorSkillCategory.SITTING),
            MotorMilestone(6, "仰卧翻身", "能从仰卧位翻至俯卧位", MotorSkillCategory.ROLLING, True),
            
            # 7-9月里程碑
            MotorMilestone(7, "独坐直", "能独立坐直不需支撑", MotorSkillCategory.SITTING, True),
            MotorMilestone(8, "会爬", "能用四肢爬行移动", MotorSkillCategory.CRAWLING, True),
            MotorMilestone(9, "拉双手会走", "拉住双手能迈步行走", MotorSkillCategory.WALKING),
            
            # 10-12月里程碑
            MotorMilestone(10, "保护性支撑", "跌倒时能用手支撑保护", MotorSkillCategory.BALANCE),
            MotorMilestone(11, "独站片刻", "能独立站立片刻", MotorSkillCategory.STANDING, True),
            MotorMilestone(12, "独走自如", "能独立行走", MotorSkillCategory.WALKING, True),
            
            # 13-18月里程碑
            MotorMilestone(15, "脚尖走", "能用脚尖行走", MotorSkillCategory.WALKING),
            MotorMilestone(18, "扶楼梯上楼", "扶着栏杆能上楼梯", MotorSkillCategory.COORDINATION),
            
            # 19-24月里程碑
            MotorMilestone(20, "扔球无方向", "能扔球但无特定方向", MotorSkillCategory.COORDINATION),
            MotorMilestone(24, "双足跳离地面", "双脚能同时离地跳跃", MotorSkillCategory.JUMPING, True),
            
            # 25-36月里程碑
            MotorMilestone(30, "立定跳远", "能做立定跳远动作", MotorSkillCategory.JUMPING),
            MotorMilestone(36, "双脚交替跳", "能双脚交替跳跃", MotorSkillCategory.JUMPING, True),
        ]
        return milestones
    
    def create_assessment_entry(self, child_age_months: int, behaviors: List[str], 
                              assessment: str, recommendations: List[str]) -> AssessmentEntry:
        """创建评估类条目"""
        entry = AssessmentEntry()
        entry.child_age_months = child_age_months
        entry.age_group = self._get_age_group(child_age_months)
        entry.interaction_type = InteractionType.ASSESSMENT
        entry.observed_behaviors = behaviors
        entry.assessment_result = assessment
        entry.recommendations = recommendations
        
        # 生成输入输出文本
        entry.input_text = f"请评估{child_age_months}个月婴儿的粗大运动发育情况。观察到的行为：{'; '.join(behaviors)}"
        entry.output_text = f"评估结果：{assessment}\n建议：{'; '.join(recommendations)}"
        
        return entry
    
    def create_guidance_entry(self, age_months: int, target_skill: MotorSkillCategory,
                            current_level: str, activities: List[str]) -> GuidanceEntry:
        """创建指导类条目"""
        entry = GuidanceEntry()
        entry.child_age_months = age_months
        entry.age_group = self._get_age_group(age_months)
        entry.interaction_type = InteractionType.GUIDANCE
        entry.target_skill = target_skill
        entry.current_level = current_level
        entry.activities = activities
        
        # 生成输入输出文本
        entry.input_text = f"我的宝宝{age_months}个月，{target_skill.value}发育情况是{current_level}，请给出指导建议。"
        entry.output_text = f"针对{target_skill.value}的发育指导：\n" + "\n".join([f"• {activity}" for activity in activities])
        
        return entry
    
    def _get_age_group(self, months: int) -> AgeGroup:
        """根据月龄获取年龄分组"""
        if months <= 1:
            return AgeGroup.NEWBORN
        elif months <= 3:
            return AgeGroup.INFANT_1_3
        elif months <= 6:
            return AgeGroup.INFANT_4_6
        elif months <= 9:
            return AgeGroup.INFANT_7_9
        elif months <= 12:
            return AgeGroup.INFANT_10_12
        elif months <= 18:
            return AgeGroup.TODDLER_13_18
        elif months <= 24:
            return AgeGroup.TODDLER_19_24
        else:
            return AgeGroup.TODDLER_25_36
    
    def export_to_jsonl(self, filename: str):
        """导出为JSONL格式（适合LLM训练）"""
        with open(filename, 'w', encoding='utf-8') as f:
            for entry in self.entries:
                json_obj = {
                    "instruction": entry.input_text,
                    "output": entry.output_text,
                    "age_group": entry.age_group.value if entry.age_group else "",
                    "interaction_type": entry.interaction_type.value if entry.interaction_type else "",
                    "metadata": entry.metadata
                }
                f.write(json.dumps(json_obj, ensure_ascii=False) + '\n')
    
    def export_to_alpaca_format(self, filename: str):
        """导出为Alpaca格式"""
        alpaca_data = []
        for entry in self.entries:
            alpaca_entry = {
                "instruction": entry.input_text,
                "input": "",
                "output": entry.output_text
            }
            alpaca_data.append(alpaca_entry)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)

    def create_generic_entry(self, input_text: str, output_text: str,
                           interaction_type: str, age_months: int = None) -> DatasetEntry:
        """创建通用数据条目"""
        entry = DatasetEntry()
        entry.input_text = input_text
        entry.output_text = output_text

        # 设置交互类型
        if interaction_type == "assessment":
            entry.interaction_type = InteractionType.ASSESSMENT
        elif interaction_type == "guidance":
            entry.interaction_type = InteractionType.GUIDANCE
        elif interaction_type == "milestone":
            entry.interaction_type = InteractionType.MILESTONE_CHECK
        else:
            entry.interaction_type = InteractionType.GUIDANCE

        # 尝试从文本中提取年龄信息
        if age_months:
            entry.age_group = self._get_age_group(age_months)
        else:
            # 简单的年龄提取逻辑
            import re
            age_match = re.search(r'(\d+)个月', input_text)
            if age_match:
                months = int(age_match.group(1))
                entry.age_group = self._get_age_group(months)

        return entry

# 示例使用
if __name__ == "__main__":
    schema = DatasetSchema()
    
    # 创建示例条目
    assessment = schema.create_assessment_entry(
        child_age_months=6,
        behaviors=["能独立坐直", "俯卧时头部能抬起90度", "会翻身"],
        assessment="发育正常，符合6个月龄预期里程碑",
        recommendations=["继续鼓励爬行练习", "提供安全的探索环境", "增加俯卧时间"]
    )
    
    guidance = schema.create_guidance_entry(
        age_months=8,
        target_skill=MotorSkillCategory.CRAWLING,
        current_level="还不会爬行，只能在原地转圈",
        activities=["在宝宝前方放置玩具吸引爬行", "协助宝宝做爬行动作", "增加俯卧时间锻炼肌肉力量"]
    )
    
    schema.entries.extend([assessment, guidance])
    
    # 导出数据
    schema.export_to_jsonl("gross_motor_dataset.jsonl")
    schema.export_to_alpaca_format("gross_motor_alpaca.json")
