"""
数据集总结报告生成器
生成最终数据集的详细总结报告
"""

import json
from collections import Counter, defaultdict
from typing import Dict, List

class DatasetSummaryReporter:
    """数据集总结报告生成器"""
    
    def __init__(self):
        self.data = []
        self.load_dataset()
    
    def load_dataset(self):
        """加载数据集"""
        try:
            with open("final_gross_motor_dataset.jsonl", 'r', encoding='utf-8') as f:
                self.data = [json.loads(line) for line in f]
            print(f"✅ 加载数据集: {len(self.data)} 条")
        except FileNotFoundError:
            print("❌ 数据集文件不存在")
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n" + "="*80)
        print("📊 婴幼儿粗大运动发育数据集 - 最终报告")
        print("="*80)
        
        # 基本统计
        self._print_basic_statistics()
        
        # 类别分布
        self._print_category_distribution()
        
        # 年龄组分布
        self._print_age_group_distribution()
        
        # 数据来源分布
        self._print_source_distribution()
        
        # 文本长度分析
        self._print_text_length_analysis()
        
        # 内容质量分析
        self._print_content_quality_analysis()
        
        # 数据集特色
        self._print_dataset_features()
        
        # 使用建议
        self._print_usage_recommendations()
    
    def _print_basic_statistics(self):
        """打印基本统计信息"""
        print(f"\n📈 基本统计信息")
        print("-" * 40)
        print(f"总数据量: {len(self.data)} 条")
        
        # 计算平均长度
        instruction_lengths = [len(item['instruction']) for item in self.data]
        output_lengths = [len(item['output']) for item in self.data]
        
        print(f"平均问题长度: {sum(instruction_lengths)/len(instruction_lengths):.1f} 字符")
        print(f"平均回答长度: {sum(output_lengths)/len(output_lengths):.1f} 字符")
        print(f"问题长度范围: {min(instruction_lengths)} - {max(instruction_lengths)} 字符")
        print(f"回答长度范围: {min(output_lengths)} - {max(output_lengths)} 字符")
    
    def _print_category_distribution(self):
        """打印类别分布"""
        print(f"\n🏷️  数据类别分布")
        print("-" * 40)
        
        categories = Counter(item.get('category', 'unknown') for item in self.data)
        total = len(self.data)
        
        category_names = {
            'assessment': '发育评估',
            'guidance': '指导建议', 
            'milestone': '里程碑',
            'reflex': '反射检查',
            'general': '一般咨询'
        }
        
        for category, count in categories.most_common():
            name = category_names.get(category, category)
            percentage = count / total * 100
            print(f"  {name} ({category}): {count} 条 ({percentage:.1f}%)")
    
    def _print_age_group_distribution(self):
        """打印年龄组分布"""
        print(f"\n👶 年龄组分布")
        print("-" * 40)
        
        age_groups = Counter(item.get('age_group', 'unknown') for item in self.data)
        total = len(self.data)
        
        # 按年龄排序
        age_order = ['0-3个月', '4-6个月', '7-12个月', '13-24个月', '25-36个月']
        
        for age_group in age_order:
            if age_group in age_groups:
                count = age_groups[age_group]
                percentage = count / total * 100
                print(f"  {age_group}: {count} 条 ({percentage:.1f}%)")
        
        # 其他年龄组
        other_ages = {k: v for k, v in age_groups.items() if k not in age_order}
        if other_ages:
            print("  其他年龄组:")
            for age_group, count in sorted(other_ages.items()):
                percentage = count / total * 100
                print(f"    {age_group}: {count} 条 ({percentage:.1f}%)")
    
    def _print_source_distribution(self):
        """打印数据来源分布"""
        print(f"\n📚 数据来源分布")
        print("-" * 40)
        
        sources = Counter(item.get('source', 'unknown') for item in self.data)
        total = len(self.data)
        
        source_names = {
            'base_generated': '基础生成数据',
            'literature': '文献提取数据',
            'professional': '专业知识数据'
        }
        
        for source, count in sources.most_common():
            name = source_names.get(source, source)
            percentage = count / total * 100
            print(f"  {name}: {count} 条 ({percentage:.1f}%)")
    
    def _print_text_length_analysis(self):
        """打印文本长度分析"""
        print(f"\n📏 文本长度分析")
        print("-" * 40)
        
        instruction_lengths = [len(item['instruction']) for item in self.data]
        output_lengths = [len(item['output']) for item in self.data]
        
        # 问题长度分布
        short_q = sum(1 for l in instruction_lengths if l < 20)
        medium_q = sum(1 for l in instruction_lengths if 20 <= l < 50)
        long_q = sum(1 for l in instruction_lengths if l >= 50)
        
        print(f"问题长度分布:")
        print(f"  短问题 (<20字符): {short_q} 条 ({short_q/len(self.data)*100:.1f}%)")
        print(f"  中等问题 (20-50字符): {medium_q} 条 ({medium_q/len(self.data)*100:.1f}%)")
        print(f"  长问题 (>50字符): {long_q} 条 ({long_q/len(self.data)*100:.1f}%)")
        
        # 回答长度分布
        short_a = sum(1 for l in output_lengths if l < 100)
        medium_a = sum(1 for l in output_lengths if 100 <= l < 300)
        long_a = sum(1 for l in output_lengths if l >= 300)
        
        print(f"回答长度分布:")
        print(f"  简短回答 (<100字符): {short_a} 条 ({short_a/len(self.data)*100:.1f}%)")
        print(f"  中等回答 (100-300字符): {medium_a} 条 ({medium_a/len(self.data)*100:.1f}%)")
        print(f"  详细回答 (>300字符): {long_a} 条 ({long_a/len(self.data)*100:.1f}%)")
    
    def _print_content_quality_analysis(self):
        """打印内容质量分析"""
        print(f"\n🎯 内容质量分析")
        print("-" * 40)
        
        # 关键词覆盖分析
        motor_keywords = ['抬头', '翻身', '坐', '爬', '站', '走', '跳', '平衡', '协调']
        age_keywords = ['个月', '月龄', '岁']
        professional_keywords = ['发育', '评估', '训练', '指导', '反射', '里程碑']
        
        motor_coverage = sum(1 for item in self.data 
                           if any(keyword in item['instruction'] + item['output'] 
                                 for keyword in motor_keywords))
        
        age_coverage = sum(1 for item in self.data 
                         if any(keyword in item['instruction'] + item['output'] 
                               for keyword in age_keywords))
        
        professional_coverage = sum(1 for item in self.data 
                                  if any(keyword in item['instruction'] + item['output'] 
                                        for keyword in professional_keywords))
        
        print(f"关键词覆盖率:")
        print(f"  粗大运动关键词: {motor_coverage/len(self.data)*100:.1f}%")
        print(f"  年龄相关关键词: {age_coverage/len(self.data)*100:.1f}%")
        print(f"  专业术语关键词: {professional_coverage/len(self.data)*100:.1f}%")
        
        # 安全提醒覆盖率
        safety_reminders = sum(1 for item in self.data 
                             if '安全' in item['output'] or '注意' in item['output'])
        print(f"  包含安全提醒: {safety_reminders/len(self.data)*100:.1f}%")
    
    def _print_dataset_features(self):
        """打印数据集特色"""
        print(f"\n✨ 数据集特色")
        print("-" * 40)
        
        features = [
            "✅ 覆盖0-36个月完整年龄段",
            "✅ 包含发育评估、指导建议、里程碑检查等多种交互类型",
            "✅ 基于专业文献提取的权威知识",
            "✅ 结合实际应用场景的问答设计",
            "✅ 每个回答都包含安全提醒和个体差异说明",
            "✅ 支持多种训练格式（JSONL、Alpaca、ChatML）",
            "✅ 经过质量控制和去重处理",
            "✅ 平衡的数据分布，避免类别偏差"
        ]
        
        for feature in features:
            print(f"  {feature}")
    
    def _print_usage_recommendations(self):
        """打印使用建议"""
        print(f"\n💡 使用建议")
        print("-" * 40)
        
        recommendations = [
            "🎯 适用场景:",
            "  • 训练婴幼儿发育咨询AI助手",
            "  • 构建专业的儿童发育评估系统",
            "  • 为家长提供科学的育儿指导",
            "  • 辅助医疗专业人员进行发育筛查",
            "",
            "📚 训练建议:",
            "  • 建议使用全部614条数据进行训练",
            "  • 可按8:1:1比例划分训练/验证/测试集",
            "  • 推荐使用指令微调(Instruction Tuning)方法",
            "  • 注意保持回答的专业性和安全性",
            "",
            "⚠️  注意事项:",
            "  • 本数据集仅供AI训练使用，不能替代专业医疗建议",
            "  • 训练后的模型应明确其辅助性质，不能用于诊断",
            "  • 建议在实际应用中加入免责声明",
            "  • 定期更新数据集以保持内容的时效性"
        ]
        
        for rec in recommendations:
            print(f"  {rec}")
    
    def save_detailed_report(self):
        """保存详细报告到文件"""
        report = {
            "dataset_info": {
                "total_samples": len(self.data),
                "creation_date": "2024",
                "version": "1.0",
                "description": "婴幼儿粗大运动发育指导数据集"
            },
            "statistics": self._get_statistics(),
            "distribution": self._get_distribution(),
            "quality_metrics": self._get_quality_metrics()
        }
        
        with open("dataset_final_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: dataset_final_report.json")
    
    def _get_statistics(self):
        """获取统计信息"""
        instruction_lengths = [len(item['instruction']) for item in self.data]
        output_lengths = [len(item['output']) for item in self.data]
        
        return {
            "total_samples": len(self.data),
            "avg_instruction_length": sum(instruction_lengths) / len(instruction_lengths),
            "avg_output_length": sum(output_lengths) / len(output_lengths),
            "min_instruction_length": min(instruction_lengths),
            "max_instruction_length": max(instruction_lengths),
            "min_output_length": min(output_lengths),
            "max_output_length": max(output_lengths)
        }
    
    def _get_distribution(self):
        """获取分布信息"""
        categories = Counter(item.get('category', 'unknown') for item in self.data)
        age_groups = Counter(item.get('age_group', 'unknown') for item in self.data)
        sources = Counter(item.get('source', 'unknown') for item in self.data)
        
        return {
            "categories": dict(categories),
            "age_groups": dict(age_groups),
            "sources": dict(sources)
        }
    
    def _get_quality_metrics(self):
        """获取质量指标"""
        motor_keywords = ['抬头', '翻身', '坐', '爬', '站', '走', '跳', '平衡', '协调']
        
        motor_coverage = sum(1 for item in self.data 
                           if any(keyword in item['instruction'] + item['output'] 
                                 for keyword in motor_keywords))
        
        safety_reminders = sum(1 for item in self.data 
                             if '安全' in item['output'] or '注意' in item['output'])
        
        return {
            "motor_keyword_coverage": motor_coverage / len(self.data),
            "safety_reminder_coverage": safety_reminders / len(self.data),
            "unique_instructions": len(set(item['instruction'] for item in self.data)),
            "unique_outputs": len(set(item['output'] for item in self.data))
        }

if __name__ == "__main__":
    reporter = DatasetSummaryReporter()
    reporter.generate_comprehensive_report()
    reporter.save_detailed_report()
